"""Test script for icon constants and fallback mappings.

This script tests the icon constants and fallback mappings without requiring Qt.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", ".."))


def test_icon_constants():
    """Test that icon constants are properly defined."""
    print("Testing Hair Studio Icon Constants...")

    try:
        # Import constants
        from cgame_avatar_factory.hair_studio.constants import (
            ICON_ADD_LINE,
            ICON_TRASH_LINE,
            ICON_SETTINGS_LINE,
            ICON_SEARCH_LINE,
            ICON_EYE_LINE,
            ICON_EYE_OFF_LINE,
            ICON_HAIR_CARD,
            ICON_HAIR_XGEN,
            ICON_HAIR_CURVE,
            QT_ICON_FALLBACKS,
        )

        print("✓ Successfully imported icon constants")

        # Test that all icon constants are strings
        icons = [
            ICON_ADD_LINE,
            ICON_TRASH_LINE,
            ICON_SETTINGS_LINE,
            ICON_SEARCH_LINE,
            ICON_EYE_LINE,
            ICON_EYE_OFF_LINE,
            ICON_HAIR_CARD,
            ICON_HAIR_XGEN,
            ICON_HAIR_CURVE,
        ]

        for icon in icons:
            assert isinstance(icon, str), f"Icon {icon} is not a string"
            assert icon.endswith(".svg"), f"Icon {icon} does not end with .svg"

        print("✓ All icon constants are valid strings ending with .svg")

        # Test that fallback mappings exist for all icons
        for icon in icons:
            assert icon in QT_ICON_FALLBACKS, f"No fallback mapping for {icon}"
            fallback = QT_ICON_FALLBACKS[icon]
            assert isinstance(fallback, str), f"Fallback for {icon} is not a string"
            assert fallback.startswith(
                "SP_"
            ), f"Fallback {fallback} does not start with SP_"

        print("✓ All icons have valid Qt standard icon fallbacks")

        # Print the mappings
        print("\nIcon to Qt Standard Icon Mappings:")
        for icon, fallback in QT_ICON_FALLBACKS.items():
            print(f"  {icon} -> {fallback}")

        return True

    except Exception as e:
        print(f"✗ Error testing icon constants: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_icon_files_exist():
    """Test if icon files exist in the project."""
    print("\nTesting Icon File Existence...")

    try:
        from cgame_avatar_factory.hair_studio.constants import (
            ICON_ADD_LINE,
            ICON_TRASH_LINE,
            ICON_SETTINGS_LINE,
            ICON_SEARCH_LINE,
            ICON_EYE_LINE,
            ICON_EYE_OFF_LINE,
            ICON_HAIR_CARD,
            ICON_HAIR_XGEN,
            ICON_HAIR_CURVE,
        )

        # Get project root - go up from test/test_icon_constants.py to project root
        test_dir = os.path.dirname(__file__)  # hair_studio/test/
        hair_studio_dir = os.path.dirname(test_dir)  # hair_studio/
        cgame_dir = os.path.dirname(hair_studio_dir)  # cgame_avatar_factory/
        project_root = os.path.dirname(cgame_dir)  # project root
        static_images_path = os.path.join(
            project_root, "cgame_avatar_factory", "resources", "static", "images"
        )

        icons = [
            ICON_ADD_LINE,
            ICON_TRASH_LINE,
            ICON_SETTINGS_LINE,
            ICON_SEARCH_LINE,
            ICON_EYE_LINE,
            ICON_EYE_OFF_LINE,
            ICON_HAIR_CARD,
            ICON_HAIR_XGEN,
            ICON_HAIR_CURVE,
        ]

        missing_icons = []
        existing_icons = []

        for icon in icons:
            icon_path = os.path.join(static_images_path, icon)
            if os.path.exists(icon_path):
                existing_icons.append(icon)
            else:
                missing_icons.append(icon)

        print(f"✓ Found {len(existing_icons)} existing icons:")
        for icon in existing_icons:
            print(f"  - {icon}")

        print(f"✗ Missing {len(missing_icons)} icons (will use fallbacks):")
        for icon in missing_icons:
            print(f"  - {icon}")

        # List available icons in the static images directory
        if os.path.exists(static_images_path):
            available_icons = [
                f for f in os.listdir(static_images_path) if f.endswith(".svg")
            ]
            print(f"\nAvailable icons in {static_images_path}:")
            for icon in available_icons:
                print(f"  - {icon}")
        else:
            print(f"\nStatic images directory does not exist: {static_images_path}")

        return True

    except Exception as e:
        print(f"✗ Error testing icon file existence: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Hair Studio Icon Fallback System Test")
    print("=" * 50)

    success1 = test_icon_constants()
    success2 = test_icon_files_exist()

    if success1 and success2:
        print("\n✓ All icon tests passed!")
        print("The fallback system should work correctly when icons are missing.")
    else:
        print("\n✗ Some icon tests failed!")
        sys.exit(1)
