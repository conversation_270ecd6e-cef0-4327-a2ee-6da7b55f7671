"""Asset Item Module.

This module provides the AssetItem widget for displaying individual hair assets
in the asset library grid.
"""

# Import standard library
import os
import json

# Import Qt modules
from qtpy import QtWidgets, QtCore, QtGui
from qtpy.QtCore import Qt

# Import dayu widgets
from dayu_widgets import MLabel, MToolButton, MFlowLayout, dayu_theme

# Import local modules
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager


class AssetItem(QtWidgets.QWidget):
    """Asset Item Widget.

    This widget represents a single hair asset in the asset library.
    It displays a thumbnail and the asset name, and emits a signal when clicked.
    """

    # Signal emitted when the item is clicked
    clicked = QtCore.Signal(dict)

    def __init__(self, asset_data, parent=None):
        """Initialize the AssetItem.

        Args:
            asset_data (dict): Dictionary containing asset data
            parent (QWidget, optional): The parent widget. Defaults to None.
        """
        super(AssetItem, self).__init__(parent)
        self.asset_data = asset_data
        self.manager = HairManager()

        # Drag and drop variables
        self._drag_start_position = None
        self._is_dragging = False

        # Selection state
        self._is_selected = False

        # Set up UI
        self.setup_ui()
        self.setSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)

        # Enable drag operations (this widget is a drag source, not a drop target)
        self.setAttribute(Qt.WA_DeleteOnClose)

    def setup_ui(self):
        """Set up the user interface components."""
        # Main layout
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # Thumbnail (placeholder for now)
        self.thumbnail_label = QtWidgets.QLabel()
        self.thumbnail_label.setFixedSize(100, 100)
        self.thumbnail_label.setStyleSheet(
            """
            QLabel {
                background-color: #2D2D2D;
                border: 1px solid #3E3E3E;
                border-radius: 3px;
            }
        """
        )

        # Set placeholder text
        self.thumbnail_label.setText("Thumbnail")
        self.thumbnail_label.setAlignment(QtCore.Qt.AlignCenter)

        # TODO: Load actual thumbnail from asset data

        # Name label
        self.name_label = MLabel(self.asset_data.get("name", "Unnamed Asset"))
        self.name_label.setAlignment(QtCore.Qt.AlignCenter)
        self.name_label.set_elide_mode(QtCore.Qt.ElideRight)
        self.name_label.setToolTip(self.name_label.text())

        # Add widgets to layout
        layout.addWidget(self.thumbnail_label, 0, QtCore.Qt.AlignHCenter)
        layout.addWidget(self.name_label, 0, QtCore.Qt.AlignHCenter)

        # Set initial style
        self._update_style()

    def mousePressEvent(self, event):
        """Handle mouse press events for both clicking and drag initiation."""
        if event.button() == Qt.LeftButton:
            # Store the position for potential drag operation
            self._drag_start_position = event.pos()
            self._is_dragging = False

            # Emit click signal for immediate selection
            self.clicked.emit(self.asset_data)

        super(AssetItem, self).mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Handle mouse move events for drag operations."""
        # Only start drag if left button is pressed and we have a start position
        if not (event.buttons() & Qt.LeftButton) or not self._drag_start_position:
            return

        # Check if the distance is large enough to start a drag
        distance = (event.pos() - self._drag_start_position).manhattanLength()
        if distance < QtWidgets.QApplication.startDragDistance():
            return

        # Prevent multiple drag operations
        if self._is_dragging:
            return

        self._is_dragging = True
        self._start_drag()

    def mouseDoubleClickEvent(self, event):
        """Handle mouse double click events."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.asset_data)

        super(AssetItem, self).mouseDoubleClickEvent(event)

    def _start_drag(self):
        """Start the drag operation with asset data."""
        try:
            # Create drag object
            drag = QtGui.QDrag(self)
            mime_data = QtCore.QMimeData()

            # Set asset data as JSON text
            asset_json = json.dumps(self.asset_data)
            mime_data.setText(asset_json)

            # Set custom MIME type for hair studio assets
            mime_data.setData(
                "application/x-hair-asset",
                QtCore.QByteArray(asset_json.encode("utf-8")),
            )

            # Create drag pixmap (visual feedback)
            drag_pixmap = self._create_drag_pixmap()
            if drag_pixmap:
                drag.setPixmap(drag_pixmap)
                # Set hotspot to center of pixmap
                drag.setHotSpot(
                    QtCore.QPoint(drag_pixmap.width() // 2, drag_pixmap.height() // 2)
                )

            # Set mime data
            drag.setMimeData(mime_data)

            # Execute drag operation
            drop_action = drag.exec_(Qt.CopyAction)

            # Reset drag state
            self._is_dragging = False
            self._drag_start_position = None

        except Exception as e:
            print("Error starting drag operation: {}".format(str(e)))
            self._is_dragging = False
            self._drag_start_position = None

    def _create_drag_pixmap(self):
        """Create a pixmap for drag visual feedback.

        Returns:
            QtGui.QPixmap: Pixmap for drag operation, or None if creation fails
        """
        try:
            # Create a pixmap of this widget
            pixmap = self.grab()

            # Make it semi-transparent for drag effect
            transparent_pixmap = QtGui.QPixmap(pixmap.size())
            transparent_pixmap.fill(Qt.transparent)

            painter = QtGui.QPainter(transparent_pixmap)
            painter.setOpacity(0.7)  # 70% opacity
            painter.drawPixmap(0, 0, pixmap)
            painter.end()

            return transparent_pixmap

        except Exception as e:
            print("Error creating drag pixmap: {}".format(str(e)))
            return None

    def get_asset_data(self):
        """Get the asset data for this item.

        Returns:
            dict: The asset data
        """
        return self.asset_data

    def set_thumbnail(self, pixmap):
        """Set the thumbnail image for this asset.

        Args:
            pixmap (QPixmap): The thumbnail image
        """
        if pixmap and not pixmap.isNull():
            scaled_pixmap = pixmap.scaled(
                self.thumbnail_label.size(),
                QtCore.Qt.KeepAspectRatio,
                QtCore.Qt.SmoothTransformation,
            )
            self.thumbnail_label.setPixmap(scaled_pixmap)
            self.thumbnail_label.setText("")  # Clear placeholder text

    def set_selected(self, selected):
        """Set the selection state of this asset item.

        Args:
            selected (bool): True to select, False to deselect
        """
        if self._is_selected != selected:
            self._is_selected = selected
            self._update_style()

    def is_selected(self):
        """Check if this asset item is selected.

        Returns:
            bool: True if selected, False otherwise
        """
        return self._is_selected

    def _update_style(self):
        """Update the widget style based on selection state."""
        if self._is_selected:
            # Selected state style
            self.setStyleSheet(
                """
                QWidget {
                    background: rgba(64, 128, 255, 0.2);
                    border: 2px solid #4080FF;
                    border-radius: 4px;
                }
                QWidget:hover {
                    background: rgba(64, 128, 255, 0.3);
                    border: 2px solid #4080FF;
                }
            """
            )
        else:
            # Normal state style
            self.setStyleSheet(
                """
                QWidget {
                    background: transparent;
                    border: 1px solid transparent;
                    border-radius: 4px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid #3E3E3E;
                }
            """
            )
