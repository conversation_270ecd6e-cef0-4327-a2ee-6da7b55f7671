"""Simple test to verify MListView compatibility fixes without GUI."""

import sys
import os

# Add project path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def test_imports():
    """Test that imports work correctly."""
    try:
        from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
        print("✓ ComponentList import successful")
        return True
    except Exception as e:
        print(f"✗ ComponentList import failed: {e}")
        return False

def test_method_existence():
    """Test that all fixed methods exist."""
    try:
        from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
        
        # Check if all the fixed methods exist
        methods_to_check = [
            '_show_context_menu',
            '_rename_selected_component', 
            '_duplicate_selected_component',
            '_toggle_selected_component_visibility',
            '_delete_selected_components',
            'get_selected_components',
            'get_component_order',
            'reorder_components'
        ]
        
        for method_name in methods_to_check:
            if hasattr(ComponentList, method_name):
                print(f"✓ Method {method_name} exists")
            else:
                print(f"✗ Method {method_name} missing")
                return False
        
        return True
    except Exception as e:
        print(f"✗ Method check failed: {e}")
        return False

def main():
    """Main test function."""
    print("Testing MListView Compatibility Fixes")
    print("=" * 40)
    
    # Test imports
    if not test_imports():
        return False
    
    # Test method existence
    if not test_method_existence():
        return False
    
    print("\n=== Summary ===")
    print("✓ All basic tests passed!")
    print("✓ ComponentList should now be compatible with MListView")
    print("✓ Fixed methods:")
    print("  - _show_context_menu (count() method)")
    print("  - _rename_selected_component (currentItem() method)")
    print("  - _duplicate_selected_component (currentItem() method)")
    print("  - _toggle_selected_component_visibility (currentItem() method)")
    print("  - _delete_selected_components (selectedItems() method)")
    print("  - get_selected_components (selectedItems() method)")
    print("  - get_component_order (count()/item() methods)")
    print("  - reorder_components (count()/item()/clear()/addItem() methods)")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
