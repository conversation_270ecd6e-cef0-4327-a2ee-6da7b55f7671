"""Visual test for ComponentItem display.

This script creates a simple Qt application to visually test the ComponentItem layout
and icon display without requiring the full Hair Studio environment.
"""

import sys
import os
import logging

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, project_root)

def create_visual_test():
    """Create a visual test for ComponentItem."""
    print("Creating Visual Test for ComponentItem...")
    
    try:
        # Import Qt
        from qtpy import QtWidgets, QtCore, QtGui
        
        # Import Hair Studio modules
        from cgame_avatar_factory.hair_studio.ui.component_item import ComponentItem
        
        # Set up logging
        logging.basicConfig(level=logging.DEBUG)
        
        # Create application
        app = QtWidgets.QApplication(sys.argv)
        
        # Create main window
        window = QtWidgets.QMainWindow()
        window.setWindowTitle("Hair Studio ComponentItem Visual Test")
        window.setGeometry(100, 100, 600, 400)
        
        # Create central widget
        central_widget = QtWidgets.QWidget()
        window.setCentralWidget(central_widget)
        
        # Create layout
        layout = QtWidgets.QVBoxLayout(central_widget)
        
        # Add title
        title = QtWidgets.QLabel("Hair Studio ComponentItem Visual Test")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Add description
        desc = QtWidgets.QLabel(
            "Testing ComponentItem layout: Eye Icon -> Asset Icon -> Name\n"
            "Icons should use Qt standard fallbacks if custom icons are missing."
        )
        desc.setStyleSheet("margin: 10px; color: gray;")
        layout.addWidget(desc)
        
        # Create test component data
        test_components = [
            {
                "id": "card_1",
                "name": "Hair Card Component",
                "type": "card",
                "is_viewed": True,
                "asset_id": "card_1",
            },
            {
                "id": "xgen_1", 
                "name": "XGen Hair Component",
                "type": "xgen",
                "is_viewed": False,
                "asset_id": "xgen_1",
            },
            {
                "id": "curve_1",
                "name": "Hair Curve Component",
                "type": "curve", 
                "is_viewed": True,
                "asset_id": "curve_1",
            },
        ]
        
        # Create component items
        for i, component_data in enumerate(test_components):
            try:
                # Create component item
                component_item = ComponentItem(component_data)
                
                # Add to layout
                layout.addWidget(component_item)
                
                print(f"✓ Created component item {i+1}: {component_data['name']}")
                
            except Exception as e:
                print(f"✗ Failed to create component item {i+1}: {e}")
                # Create a simple label as fallback
                fallback_label = QtWidgets.QLabel(f"Failed to create: {component_data['name']}")
                fallback_label.setStyleSheet("color: red; margin: 5px;")
                layout.addWidget(fallback_label)
        
        # Add spacer
        layout.addStretch()
        
        # Add instructions
        instructions = QtWidgets.QLabel(
            "Instructions:\n"
            "- Each row should show: [Eye Icon] [Asset Icon] [Component Name]\n"
            "- Click eye icons to toggle visibility\n"
            "- Icons may appear as Qt standard icons or colored squares\n"
            "- Check console for debug messages about icon loading"
        )
        instructions.setStyleSheet("margin: 10px; font-size: 10px; color: #666;")
        layout.addWidget(instructions)
        
        # Show window
        window.show()
        
        print("✓ Visual test window created and displayed")
        print("✓ Check the window to verify ComponentItem layout")
        print("✓ Look for: Eye Icon -> Asset Icon -> Component Name")
        
        # Run application
        return app.exec_()
        
    except ImportError as e:
        print(f"✗ Import error (Qt not available): {e}")
        print("This test requires a Qt environment to run.")
        return False
    except Exception as e:
        print(f"✗ Visual test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Hair Studio ComponentItem Visual Test")
    print("=" * 50)
    
    success = create_visual_test()
    
    if success:
        print("\n✓ Visual test completed successfully!")
    else:
        print("\n✗ Visual test failed!")
        sys.exit(1)
