import sys
from pathlib import Path

# This file is automatically discovered by pytest.
# It adds the project's root directory to the Python path to resolve module import errors.

# The path to this conftest.py file is: .../cgame_avatar_factory/hair_studio/test/conftest.py
# The project root we need to add to the path is four levels up.
project_root = Path(__file__).resolve().parent.parent.parent.parent

if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))
