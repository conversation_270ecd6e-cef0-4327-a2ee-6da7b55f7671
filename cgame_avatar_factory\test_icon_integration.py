"""Integration test for Hair Studio icon fallback system.

This script tests the complete icon integration without requiring Maya or full UI startup.
"""

import sys
import os
import logging

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # Go up one level to project root
sys.path.insert(0, project_root)


def test_icon_integration():
    """Test the complete icon integration."""
    print("Hair Studio Icon Integration Test")
    print("=" * 50)

    # Set up logging
    logging.basicConfig(level=logging.DEBUG)

    try:
        # Test 1: Import icon constants
        print("\n1. Testing icon constants import...")
        from cgame_avatar_factory.hair_studio.constants import (
            ICON_ADD_LINE,
            ICON_TRASH_LINE,
            ICON_SETTINGS_LINE,
            ICON_SEARCH_LINE,
            ICON_EYE_LINE,
            ICON_EYE_OFF_LINE,
            ICON_HAIR_CARD,
            ICON_HAIR_XGEN,
            ICON_HAIR_CURVE,
            QT_ICON_FALLBACKS,
        )

        print("✓ Icon constants imported successfully")

        # Test 2: Check icon mappings
        print("\n2. Testing icon mappings...")
        icons = [
            ICON_ADD_LINE,
            ICON_TRASH_LINE,
            ICON_SETTINGS_LINE,
            ICON_SEARCH_LINE,
            ICON_EYE_LINE,
            ICON_EYE_OFF_LINE,
            ICON_HAIR_CARD,
            ICON_HAIR_XGEN,
            ICON_HAIR_CURVE,
        ]

        for icon in icons:
            assert icon in QT_ICON_FALLBACKS, f"Missing fallback for {icon}"
            fallback = QT_ICON_FALLBACKS[icon]
            print(f"  {icon} -> {fallback}")

        print("✓ All icon mappings are valid")

        # Test 3: Check existing icon files
        print("\n3. Testing existing icon files...")
        static_path = os.path.join(
            project_root, "cgame_avatar_factory", "resources", "static", "images"
        )

        existing_icons = []
        missing_icons = []

        for icon in icons:
            icon_path = os.path.join(static_path, icon)
            if os.path.exists(icon_path):
                existing_icons.append(icon)
                print(f"  ✓ Found: {icon}")
            else:
                missing_icons.append(icon)
                print(f"  ✗ Missing: {icon} (will use fallback)")

        print(f"\n✓ Found {len(existing_icons)} existing icons")
        print(f"✓ {len(missing_icons)} icons will use Qt fallbacks")

        # Test 4: Test icon utility functions (without Qt)
        print("\n4. Testing icon utility functions...")
        try:
            from cgame_avatar_factory.hair_studio.utils.icon_utils import (
                QT_ICON_FALLBACKS as utils_fallbacks,
            )

            print("✓ Icon utilities imported successfully")

            # Verify fallback mappings are consistent
            assert (
                utils_fallbacks == QT_ICON_FALLBACKS
            ), "Fallback mappings inconsistent"
            print("✓ Fallback mappings are consistent")

        except ImportError as e:
            print(f"⚠ Icon utilities import failed (expected without Qt): {e}")

        # Test 5: Test Hair Studio module imports
        print("\n5. Testing Hair Studio module imports...")
        try:
            from cgame_avatar_factory.hair_studio.manager.hair_manager import (
                HairManager,
            )

            print("✓ HairManager imported successfully")

            manager = HairManager()
            print("✓ HairManager created successfully")

        except Exception as e:
            print(f"✗ HairManager test failed: {e}")
            return False

        # Test 6: Test component imports (without UI creation)
        print("\n6. Testing component imports...")
        try:
            # These imports should work even without Qt
            from cgame_avatar_factory.hair_studio.constants import UI_TEXT_CARD_TAB
            from cgame_avatar_factory.hair_studio.data.models import HairAsset

            print("✓ Core components imported successfully")

        except Exception as e:
            print(f"✗ Component import test failed: {e}")
            return False

        print("\n" + "=" * 50)
        print("✓ ALL ICON INTEGRATION TESTS PASSED!")
        print("=" * 50)
        print("\nSummary:")
        print(f"- {len(existing_icons)} icons will use existing files")
        print(f"- {len(missing_icons)} icons will use Qt standard fallbacks")
        print("- Icon fallback system is properly configured")
        print("- Hair Studio modules can be imported successfully")
        print("\nThe UI should now display correctly with proper icon fallbacks!")

        return True

    except Exception as e:
        print(f"\n✗ Icon integration test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_icon_integration()
    if not success:
        sys.exit(1)
