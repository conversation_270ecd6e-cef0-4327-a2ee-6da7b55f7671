"""Test script for icon fallback functionality.

This script tests the icon fallback system to ensure Qt standard icons
are used when custom icons are not available.
"""

import sys
import logging

# Import Qt modules
from qtpy import QtWidgets, QtCore

# Import local modules
from cgame_avatar_factory.hair_studio.utils.icon_utils import (
    get_icon_with_fallback,
    get_qt_standard_icon,
    create_default_icon,
    set_button_icon_with_fallback,
    set_label_pixmap_with_fallback,
)
from cgame_avatar_factory.hair_studio.constants import (
    ICON_ADD_LINE,
    ICON_TRASH_LINE,
    ICON_SETTINGS_LINE,
    ICON_SEARCH_LINE,
    ICON_EYE_LINE,
    ICON_EYE_OFF_LINE,
    ICON_HAIR_CARD,
    ICON_HAIR_XGEN,
    ICON_HAIR_CURVE,
)


class IconTestWidget(QtWidgets.QWidget):
    """Test widget to display all icons with fallbacks."""
    
    def __init__(self):
        super(IconTestWidget, self).__init__()
        self.setWindowTitle("Hair Studio Icon Fallback Test")
        self.setFixedSize(600, 400)
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the test UI."""
        layout = QtWidgets.QVBoxLayout(self)
        
        # Title
        title = QtWidgets.QLabel("Hair Studio Icon Fallback Test")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Scroll area for icons
        scroll = QtWidgets.QScrollArea()
        scroll_widget = QtWidgets.QWidget()
        scroll_layout = QtWidgets.QGridLayout(scroll_widget)
        
        # Test all icons
        icons_to_test = [
            ("Add", ICON_ADD_LINE),
            ("Trash", ICON_TRASH_LINE),
            ("Settings", ICON_SETTINGS_LINE),
            ("Search", ICON_SEARCH_LINE),
            ("Eye", ICON_EYE_LINE),
            ("Eye Off", ICON_EYE_OFF_LINE),
            ("Hair Card", ICON_HAIR_CARD),
            ("Hair XGen", ICON_HAIR_XGEN),
            ("Hair Curve", ICON_HAIR_CURVE),
        ]
        
        for i, (name, icon_name) in enumerate(icons_to_test):
            row = i // 3
            col = i % 3
            
            # Create a group for each icon
            group = QtWidgets.QGroupBox(name)
            group_layout = QtWidgets.QVBoxLayout(group)
            
            # Icon label
            icon_label = QtWidgets.QLabel()
            icon_label.setFixedSize(32, 32)
            icon_label.setAlignment(QtCore.Qt.AlignCenter)
            set_label_pixmap_with_fallback(icon_label, icon_name, 32, 32)
            group_layout.addWidget(icon_label)
            
            # Icon button
            icon_button = QtWidgets.QPushButton("Button")
            set_button_icon_with_fallback(icon_button, icon_name)
            group_layout.addWidget(icon_button)
            
            # Icon name label
            name_label = QtWidgets.QLabel(icon_name)
            name_label.setWordWrap(True)
            name_label.setStyleSheet("font-size: 10px; color: gray;")
            group_layout.addWidget(name_label)
            
            scroll_layout.addWidget(group, row, col)
        
        scroll.setWidget(scroll_widget)
        layout.addWidget(scroll)
        
        # Status label
        status_label = QtWidgets.QLabel(
            "All icons should display using Qt standard icons as fallbacks."
        )
        status_label.setStyleSheet("margin: 10px; color: green;")
        layout.addWidget(status_label)


def test_icon_fallback_system():
    """Test the icon fallback system."""
    print("Testing Hair Studio Icon Fallback System...")
    
    # Set up logging
    logging.basicConfig(level=logging.DEBUG)
    
    # Create QApplication if it doesn't exist
    app = QtWidgets.QApplication.instance()
    if app is None:
        app = QtWidgets.QApplication(sys.argv)
    
    try:
        # Test individual icon functions
        print("\n1. Testing get_icon_with_fallback...")
        icon = get_icon_with_fallback(ICON_ADD_LINE)
        print(f"✓ Got icon for {ICON_ADD_LINE}: {type(icon)}")
        
        print("\n2. Testing get_qt_standard_icon...")
        qt_icon = get_qt_standard_icon(ICON_TRASH_LINE)
        print(f"✓ Got Qt standard icon for {ICON_TRASH_LINE}: {type(qt_icon)}")
        
        print("\n3. Testing create_default_icon...")
        default_icon = create_default_icon((24, 24))
        print(f"✓ Created default icon: {type(default_icon)}")
        
        print("\n4. Creating test widget...")
        test_widget = IconTestWidget()
        test_widget.show()
        
        print("\n✓ Icon fallback test widget created successfully!")
        print("Check the widget to see if all icons are displayed correctly.")
        
        # Don't exec the app in test mode, just show the widget
        return True, test_widget
        
    except Exception as e:
        print(f"✗ Error during icon fallback test: {e}")
        import traceback
        traceback.print_exc()
        return False, None


if __name__ == "__main__":
    success, widget = test_icon_fallback_system()
    if success and widget:
        # Run the application if this script is executed directly
        app = QtWidgets.QApplication.instance()
        if app:
            sys.exit(app.exec_())
    else:
        print("Icon fallback test failed!")
        sys.exit(1)
