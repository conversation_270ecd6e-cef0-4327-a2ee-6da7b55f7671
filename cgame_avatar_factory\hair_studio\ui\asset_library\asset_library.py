"""Asset Library Module.

This module provides the asset library widget for the Hair Studio tool.
It displays available hair assets in a grid layout and allows users to select
and drag them to the component list.
"""

# Import standard library
import os
import logging

# Import Qt modules
from qtpy import QtWidgets, QtCore

# Import dayu widgets
from dayu_widgets import MLabel, MLineEdit, MFlowLayout, MPushButton, MToolButton


# Import local modules
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_item import AssetItem
from cgame_avatar_factory.hair_studio.constants import (
    UI_TEXT_HAIR_ASSET_LIBRARY,
    UI_TEXT_SEARCH_PLACEHOLDER,
    UI_TEXT_SETTINGS,
    LAYOUT_MARGIN_ZERO,
    LAYOUT_SPACING_SMALL,
    OBJECT_NAME_ASSET_LIBRARY_FORMAT,
    ICON_SEARCH_LINE,
    ICON_SETTINGS_LINE,
)
from cgame_avatar_factory.hair_studio.utils.icon_utils import (
    set_button_icon_with_fallback,
)


class AssetLibrary(QtWidgets.QScrollArea):
    """Asset Library Widget.

    This widget displays a grid of hair assets that can be dragged to the component list.
    It supports searching and filtering of assets.
    """

    # Signal emitted when an asset is selected
    asset_selected = QtCore.Signal(dict)

    def __init__(self, hair_type, hair_manager=None, parent=None, logger=None):
        """Initialize the AssetLibrary.

        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager, optional): The hair manager instance. If None, a new one will be created.
            parent (QWidget, optional): The parent widget. Defaults to None.
            logger (logging.Logger, optional): Logger instance to use. If None, creates a new one.
        """
        super(AssetLibrary, self).__init__(parent)
        self.hair_type = hair_type
        self.object_name = OBJECT_NAME_ASSET_LIBRARY_FORMAT.format(hair_type)
        self.setObjectName(self.object_name)

        # Initialize logger - use provided logger or create new one
        self._logger = logger if logger is not None else logging.getLogger(__name__)

        # Track selected asset item for single selection mode
        self._selected_asset_item = None

        # Initialize manager
        self.manager = (
            hair_manager
            if hair_manager is not None
            else HairManager(logger=self._logger)
        )

        # List of assets
        self.assets = []

        # Initialize UI
        self.setup_ui()

        # Load initial assets
        self.refresh()

    def setup_ui(self):
        """Set up the user interface components."""
        # Create container widget
        container = QtWidgets.QWidget()
        self.setWidget(container)
        self.setWidgetResizable(True)

        # Main layout
        main_layout = QtWidgets.QVBoxLayout(container)
        main_layout.setContentsMargins(
            LAYOUT_SPACING_SMALL,
            LAYOUT_SPACING_SMALL,
            LAYOUT_SPACING_SMALL,
            LAYOUT_SPACING_SMALL,
        )
        main_layout.setSpacing(LAYOUT_SPACING_SMALL)

        # Header layout with title and settings button
        header_layout = QtWidgets.QHBoxLayout()
        header_layout.setContentsMargins(
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
        )
        header_layout.setSpacing(LAYOUT_SPACING_SMALL)

        # Title
        title = MLabel(UI_TEXT_HAIR_ASSET_LIBRARY)
        title.setProperty("h2", True)
        header_layout.addWidget(title)

        # Add stretch to push settings button to the right
        header_layout.addStretch()

        # Settings button (gear icon)
        self.settings_btn = MToolButton()
        set_button_icon_with_fallback(self.settings_btn, ICON_SETTINGS_LINE)
        self.settings_btn.setToolTip(UI_TEXT_SETTINGS)
        self.settings_btn.clicked.connect(self._on_settings_clicked)
        header_layout.addWidget(self.settings_btn)

        main_layout.addLayout(header_layout)

        # Search bar with icon
        search_layout = QtWidgets.QHBoxLayout()
        search_layout.setContentsMargins(
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
        )
        search_layout.setSpacing(LAYOUT_SPACING_SMALL)

        # Search icon
        search_icon = MToolButton()
        set_button_icon_with_fallback(search_icon, ICON_SEARCH_LINE)
        search_icon.setEnabled(False)  # Just for display
        search_layout.addWidget(search_icon)

        # Search input
        self.search_edit = MLineEdit()
        self.search_edit.setPlaceholderText(UI_TEXT_SEARCH_PLACEHOLDER)
        self.search_edit.textChanged.connect(self._on_search_text_changed)
        search_layout.addWidget(self.search_edit)

        main_layout.addLayout(search_layout)

        # Add separator
        separator = QtWidgets.QFrame()
        separator.setFrameShape(QtWidgets.QFrame.HLine)
        separator.setFrameShadow(QtWidgets.QFrame.Sunken)
        main_layout.addWidget(separator)

        # Assets grid container
        self.assets_container = QtWidgets.QWidget()
        self.assets_layout = QtWidgets.QGridLayout(self.assets_container)
        self.assets_layout.setSpacing(10)
        self.assets_layout.setContentsMargins(
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
        )

        # Add scroll area for assets
        scroll_area = QtWidgets.QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.assets_container)
        main_layout.addWidget(scroll_area)

        # Set size policy
        self.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding
        )
        self.assets_container.setSizePolicy(
            QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding
        )

    def refresh(self):
        """Refresh the asset library with the latest data."""
        try:
            # Clear existing assets
            self._clear_assets()

            # Get assets from manager
            self.assets = self.manager.get_assets(asset_type=self.hair_type)

            # Log asset retrieval for debugging
            self._logger.info(
                "AssetLibrary refresh: Retrieved %d assets for type '%s'",
                len(self.assets),
                self.hair_type,
            )

            if self.assets:
                for asset in self.assets:
                    self._logger.debug(
                        "Asset: %s (ID: %s, Type: %s)",
                        asset.get("name", "Unknown"),
                        asset.get("id", "Unknown"),
                        asset.get("asset_type", "Unknown"),
                    )

            # Update the UI
            self._update_assets_grid()

        except Exception as e:
            self._logger.error(
                "Error refreshing asset library: %s", str(e), exc_info=True
            )

    def update_assets(self, assets):
        """Update the asset library with the provided assets.

        Args:
            assets (list): List of asset dictionaries to display
        """
        # Update the assets list
        self.assets = assets if assets else []

        # Update the UI
        self._update_assets_grid()

    def _clear_assets(self):
        """Clear all assets from the grid and data."""
        # Remove all widgets from the layout
        self._clear_ui_widgets()

        # Clear the assets list (used only in refresh())
        self.assets = []

    def _clear_ui_widgets(self):
        """Clear only the UI widgets from the grid, keep the data."""
        # Remove all widgets from the layout
        while self.assets_layout.count():
            item = self.assets_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()

    def _update_assets_grid(self, filter_text=""):
        """Update the assets grid with the current assets.

        Args:
            filter_text (str, optional): Text to filter assets by name. Defaults to ''.
        """
        try:
            # Clear existing UI widgets only (not the data)
            self._clear_ui_widgets()

            # Filter assets by name if filter text is provided
            filtered_assets = [
                asset
                for asset in self.assets
                if not filter_text or filter_text.lower() in asset["name"].lower()
            ]

            self._logger.info(
                "Updating assets grid: %d assets (filtered from %d)",
                len(filtered_assets),
                len(self.assets),
            )

            # Add assets to the grid
            max_columns = 3
            for i, asset in enumerate(filtered_assets):
                try:
                    row = i // max_columns
                    col = i % max_columns

                    # Create asset item widget
                    asset_item = AssetItem(asset, self)
                    asset_item.setSizePolicy(
                        QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed
                    )
                    asset_item.setFixedSize(120, 140)

                    # Connect signals for selection (not creation)
                    asset_item.clicked.connect(
                        lambda asset=asset, item=asset_item: self._on_asset_item_clicked(
                            asset, item
                        )
                    )

                    # Add to grid
                    self.assets_layout.addWidget(asset_item, row, col)

                    self._logger.debug(
                        "Added asset item: %s at position (%d, %d)",
                        asset.get("name", "Unknown"),
                        row,
                        col,
                    )

                except Exception as e:
                    self._logger.error(
                        "Error creating asset item for %s: %s",
                        asset.get("name", "Unknown"),
                        str(e),
                    )

            # Add stretch to push items to the top
            if self.assets_layout.rowCount() > 0:
                self.assets_layout.setRowStretch(self.assets_layout.rowCount(), 1)

            self._logger.info(
                "Assets grid update completed: %d items displayed", len(filtered_assets)
            )

        except Exception as e:
            self._logger.error("Error updating assets grid: %s", str(e), exc_info=True)

    def _on_search_text_changed(self, text):
        """Handle search text changes.

        Args:
            text (str): The search text
        """
        self._update_assets_grid(filter_text=text)

    def _on_settings_clicked(self):
        """Handle settings button click."""
        # TODO: Implement settings dialog
        QtWidgets.QMessageBox.information(
            self,
            UI_TEXT_SETTINGS,
            "Settings functionality will be implemented in future versions",
        )

    def _on_asset_item_clicked(self, asset_data, asset_item):
        """Handle asset item click for selection (not creation).

        Args:
            asset_data (dict): The asset data
            asset_item (AssetItem): The clicked asset item widget
        """
        try:
            # Clear previous selection
            if self._selected_asset_item and self._selected_asset_item != asset_item:
                self._selected_asset_item.set_selected(False)

            # Set new selection
            self._selected_asset_item = asset_item
            asset_item.set_selected(True)

            # Emit selection signal (for highlighting only, not component creation)
            self.asset_selected.emit(asset_data)

            self._logger.debug(
                "Asset selected for highlighting: %s", asset_data.get("name", "Unknown")
            )

        except Exception as e:
            self._logger.error(
                "Error handling asset item click: %s", str(e), exc_info=True
            )

    def clear_selection(self):
        """Clear the current asset selection."""
        if self._selected_asset_item:
            self._selected_asset_item.set_selected(False)
            self._selected_asset_item = None

    def get_selected_asset(self):
        """Get the currently selected asset data.

        Returns:
            dict or None: The selected asset data, or None if no selection
        """
        if self._selected_asset_item:
            return self._selected_asset_item.get_asset_data()
        return None
