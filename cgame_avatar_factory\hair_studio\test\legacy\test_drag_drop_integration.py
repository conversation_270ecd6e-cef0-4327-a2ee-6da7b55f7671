"""Drag-Drop Integration Tests for Hair Studio.

This module provides comprehensive integration tests for drag-drop functionality
between the Asset Library and Component List in the Hair Studio.
"""

# Import standard library
import sys
import time
import logging
from unittest.mock import Mock, patch

# Import Qt modules
from qtpy import QtWidgets, QtCore, QtTest
from qtpy.QtCore import Qt

# Import test framework
from cgame_avatar_factory.hair_studio.test.test_ui_automation import UITestFramework, setup_test_logging

# Import local modules
try:
    from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
    from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
    from cgame_avatar_factory.hair_studio.ui.asset_library.asset_item import AssetItem
    from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
    from cgame_avatar_factory.hair_studio.data.mock_data_manager import MockDataManager
except ImportError as e:
    print(f"Warning: Could not import Hair Studio modules: {e}")


class DragDropIntegrationTests:
    """Integration tests for drag-drop functionality."""
    
    def __init__(self, logger=None):
        """Initialize the drag-drop integration tests.
        
        Args:
            logger: Logger instance for test output
        """
        self.logger = logger or logging.getLogger(__name__)
        self.framework = UITestFramework(logger)
        self.test_results = []
        
    def run_all_tests(self):
        """Run all drag-drop integration tests.
        
        Returns:
            bool: True if all tests passed, False otherwise
        """
        self.logger.info("🧪 Starting Drag-Drop Integration Tests")
        
        if not self.framework.setup_test_environment():
            self.logger.error("Failed to setup test environment")
            return False
        
        tests = [
            ("Basic Drag-Drop Test", self.test_basic_drag_drop),
            ("Multiple Asset Types Test", self.test_multiple_asset_types),
            ("Component List Update Test", self.test_component_list_update),
            ("Selection After Drop Test", self.test_selection_after_drop),
            ("Error Handling Test", self.test_error_handling),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            self.logger.info(f"\n{'='*20} {test_name} {'='*20}")
            try:
                if test_func():
                    self.logger.info(f"✅ {test_name} PASSED")
                    passed_tests += 1
                else:
                    self.logger.error(f"❌ {test_name} FAILED")
            except Exception as e:
                self.logger.error(f"❌ {test_name} FAILED with exception: {e}")
                import traceback
                traceback.print_exc()
        
        # Cleanup
        self.framework.cleanup_test_environment()
        
        # Summary
        self.logger.info(f"\n{'='*50}")
        self.logger.info(f"Test Results: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            self.logger.info("🎉 All drag-drop integration tests PASSED!")
            return True
        else:
            self.logger.error(f"💥 {total_tests - passed_tests} tests FAILED!")
            return False
    
    def test_basic_drag_drop(self):
        """Test basic drag-drop functionality from asset library to component list.
        
        Returns:
            bool: True if test passed, False otherwise
        """
        try:
            # Create test components
            hair_manager = HairManager()
            component_list = ComponentList("card", hair_manager)
            
            # Get initial component count
            initial_count = 0
            model = component_list.component_list.model()
            if model:
                initial_count = model.rowCount()
            elif hasattr(component_list.component_list, "count"):
                initial_count = component_list.component_list.count()
            
            # Create test asset data
            test_asset = {
                "id": "test_asset_001",
                "name": "Test Hair Card",
                "asset_type": "card",
                "thumbnail": None,
                "file_path": "/test/path/hair_card.fbx"
            }
            
            # Create mock asset item
            asset_item = AssetItem(test_asset)
            
            # Simulate drag-drop
            success = self.framework.simulate_drag_drop(
                asset_item, 
                component_list, 
                test_asset,
                delay_ms=200
            )
            
            if not success:
                self.logger.error("Failed to simulate drag-drop")
                return False
            
            # Wait for UI updates
            self.framework.wait_for_ui_update(1000)
            
            # Verify component was added
            expected_count = initial_count + 1
            if not self.framework.verify_component_list_count(component_list, expected_count):
                return False
            
            # Verify component is selected
            if not self.framework.verify_component_selected(component_list, "Test Hair Card"):
                return False
            
            self.logger.info("✓ Basic drag-drop test completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Basic drag-drop test failed: {e}")
            return False
    
    def test_multiple_asset_types(self):
        """Test drag-drop with different asset types.
        
        Returns:
            bool: True if test passed, False otherwise
        """
        try:
            asset_types = ["card", "xgen", "curve"]
            
            for asset_type in asset_types:
                self.logger.info(f"Testing {asset_type} asset type...")
                
                # Create component list for this asset type
                hair_manager = HairManager()
                component_list = ComponentList(asset_type, hair_manager)
                
                # Create test asset
                test_asset = {
                    "id": f"test_{asset_type}_001",
                    "name": f"Test {asset_type.title()} Asset",
                    "asset_type": asset_type,
                    "thumbnail": None,
                    "file_path": f"/test/path/{asset_type}_asset.fbx"
                }
                
                # Create mock asset item
                asset_item = AssetItem(test_asset)
                
                # Simulate drag-drop
                success = self.framework.simulate_drag_drop(
                    asset_item,
                    component_list,
                    test_asset,
                    delay_ms=150
                )
                
                if not success:
                    self.logger.error(f"Failed to simulate drag-drop for {asset_type}")
                    return False
                
                # Wait for UI updates
                self.framework.wait_for_ui_update(500)
                
                # Verify component was added
                if not self.framework.verify_component_list_count(component_list, 1):
                    self.logger.error(f"Component count verification failed for {asset_type}")
                    return False
                
                self.logger.info(f"✓ {asset_type} asset type test passed")
            
            self.logger.info("✓ Multiple asset types test completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Multiple asset types test failed: {e}")
            return False
    
    def test_component_list_update(self):
        """Test that component list properly updates after drag-drop.
        
        Returns:
            bool: True if test passed, False otherwise
        """
        try:
            # Create test components
            hair_manager = HairManager()
            component_list = ComponentList("card", hair_manager)
            
            # Add multiple components via drag-drop
            test_assets = [
                {
                    "id": "test_card_001",
                    "name": "Hair Card 1",
                    "asset_type": "card",
                    "thumbnail": None,
                    "file_path": "/test/path/card1.fbx"
                },
                {
                    "id": "test_card_002", 
                    "name": "Hair Card 2",
                    "asset_type": "card",
                    "thumbnail": None,
                    "file_path": "/test/path/card2.fbx"
                },
                {
                    "id": "test_card_003",
                    "name": "Hair Card 3", 
                    "asset_type": "card",
                    "thumbnail": None,
                    "file_path": "/test/path/card3.fbx"
                }
            ]
            
            for i, asset in enumerate(test_assets):
                self.logger.info(f"Adding component {i+1}: {asset['name']}")
                
                # Create mock asset item
                asset_item = AssetItem(asset)
                
                # Simulate drag-drop
                success = self.framework.simulate_drag_drop(
                    asset_item,
                    component_list,
                    asset,
                    delay_ms=100
                )
                
                if not success:
                    self.logger.error(f"Failed to add component {i+1}")
                    return False
                
                # Wait for UI updates
                self.framework.wait_for_ui_update(300)
                
                # Verify count increased
                expected_count = i + 1
                if not self.framework.verify_component_list_count(component_list, expected_count):
                    self.logger.error(f"Count verification failed after adding component {i+1}")
                    return False
            
            self.logger.info("✓ Component list update test completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Component list update test failed: {e}")
            return False
    
    def test_selection_after_drop(self):
        """Test that dropped component is properly selected.
        
        Returns:
            bool: True if test passed, False otherwise
        """
        try:
            # Create test components
            hair_manager = HairManager()
            component_list = ComponentList("card", hair_manager)
            
            # Create test asset
            test_asset = {
                "id": "test_selection_001",
                "name": "Selection Test Asset",
                "asset_type": "card",
                "thumbnail": None,
                "file_path": "/test/path/selection_test.fbx"
            }
            
            # Create mock asset item
            asset_item = AssetItem(test_asset)
            
            # Simulate drag-drop
            success = self.framework.simulate_drag_drop(
                asset_item,
                component_list,
                test_asset,
                delay_ms=200
            )
            
            if not success:
                self.logger.error("Failed to simulate drag-drop")
                return False
            
            # Wait for UI updates
            self.framework.wait_for_ui_update(500)
            
            # Verify component is selected
            if not self.framework.verify_component_selected(component_list, "Selection Test Asset"):
                return False
            
            self.logger.info("✓ Selection after drop test completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Selection after drop test failed: {e}")
            return False
    
    def test_error_handling(self):
        """Test error handling in drag-drop operations.
        
        Returns:
            bool: True if test passed, False otherwise
        """
        try:
            # Create test components
            hair_manager = HairManager()
            component_list = ComponentList("card", hair_manager)
            
            # Test with invalid asset data
            invalid_asset = {
                "id": None,  # Invalid ID
                "name": "",  # Empty name
                "asset_type": "invalid_type",  # Invalid type
            }
            
            # Create mock asset item
            asset_item = AssetItem(invalid_asset)
            
            # Get initial count
            initial_count = 0
            model = component_list.component_list.model()
            if model:
                initial_count = model.rowCount()
            elif hasattr(component_list.component_list, "count"):
                initial_count = component_list.component_list.count()
            
            # Simulate drag-drop with invalid data
            success = self.framework.simulate_drag_drop(
                asset_item,
                component_list,
                invalid_asset,
                delay_ms=100
            )
            
            # Wait for UI updates
            self.framework.wait_for_ui_update(300)
            
            # Verify count didn't change (error was handled gracefully)
            if not self.framework.verify_component_list_count(component_list, initial_count):
                self.logger.error("Component was added despite invalid data")
                return False
            
            self.logger.info("✓ Error handling test completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error handling test failed: {e}")
            return False


def main():
    """Main function to run drag-drop integration tests."""
    logger = setup_test_logging()
    
    # Create and run tests
    tests = DragDropIntegrationTests(logger)
    success = tests.run_all_tests()
    
    if success:
        logger.info("🎉 All drag-drop integration tests completed successfully!")
        return 0
    else:
        logger.error("💥 Some drag-drop integration tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
