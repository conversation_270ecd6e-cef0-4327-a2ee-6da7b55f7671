"""Hair Manager Module.

This module provides the HairManager class which serves as the central point for
managing hair assets, components, and their interactions in the Hair Studio tool.
"""

# Import standard library
import os
import json
import logging
import uuid
from collections import OrderedDict

# Import third-party modules
from qtpy import QtCore

# Import local modules
from cgame_avatar_factory.hair_studio.data.models import (
    HairAsset,
    HairComponent,
    HairProject,
)
from cgame_avatar_factory.hair_studio.data.mock_data_manager import MockDataManager


class HairManager(QtCore.QObject):
    """Manager class for hair studio operations.

    This class serves as the central point for managing hair assets, components,
    and their interactions. It handles data loading, saving, and communication
    between different parts of the application.
    """

    # Signal emitted when assets are loaded
    assets_loaded = QtCore.Signal(list)

    # Signal emitted when components are updated
    components_updated = QtCore.Signal(list)

    # Signal emitted when the selected component changes
    component_selected = QtCore.Signal(object)  # HairComponent or None

    def __init__(self, parent=None, logger=None):
        """Initialize the HairManager.

        Args:
            parent: Parent QObject
            logger (logging.Logger, optional): Logger instance to use. If None, creates a new one.
        """
        super(Hair<PERSON>anager, self).__init__(parent)

        # Initialize logger - use provided logger or create new one
        self._logger = logger if logger is not None else logging.getLogger(__name__)
        if logger is None:
            self._logger.setLevel(logging.DEBUG)

        # Initialize data structures
        self._assets = OrderedDict()  # asset_id -> HairAsset
        self._components = OrderedDict()  # component_id -> HairComponent
        self._current_project = HairProject()
        self._selected_component_id = None

        # Initialize mock data manager for development and testing
        self._mock_data_manager = MockDataManager()

        # Initialize with sample data for testing
        self._initialize_sample_data()

    def _initialize_sample_data(self):
        """Initialize with sample data for testing purposes."""
        # Sample assets
        sample_assets = [
            {
                "id": "card_1",
                "name": "Hair Card 1",
                "asset_type": "card",
                "thumbnail": None,
                "metadata": {"category": "front_bangs"},
            },
            {
                "id": "xgen_1",
                "name": "XGen Hair 1",
                "asset_type": "xgen",
                "thumbnail": None,
                "metadata": {"strand_count": 1000},
            },
            {
                "id": "curve_1",
                "name": "Hair Curve 1",
                "asset_type": "curve",
                "thumbnail": None,
                "metadata": {"curve_type": "bezier"},
            }, 
        ]

        # Add sample assets
        for asset_data in sample_assets:
            asset = HairAsset(**asset_data)
            self._assets[asset.id] = asset

    def get_assets(self, asset_type=None):
        """Get a list of all assets, optionally filtered by type.

        Args:
            asset_type (str, optional): Type of assets to filter by.
                If None, returns all assets.

        Returns:
            list: List of asset dictionaries
        """
        return self._mock_data_manager.get_assets(asset_type)

    def get_components(self):
        """Get a list of all components.

        Returns:
            list: List of component dictionaries (for compatibility with mock data)
        """
        # Return mock data for development and testing
        return self._mock_data_manager.get_components()

    def get_component(self, component_id):
        """Get a component by its ID.

        Args:
            component_id (str): ID of the component to retrieve

        Returns:
            dict or None: The component data with the given ID, or None if not found
        """
        return self._mock_data_manager.get_component(component_id)

    def get_selected_component(self):
        """Get the currently selected component.

        Returns:
            HairComponent or None: The selected component, or None if none is selected
        """
        if self._selected_component_id is None:
            return None
        return self.get_component(self._selected_component_id)

    def select_component(self, component_id):
        """Select a component by its ID.

        Args:
            component_id (str or None): ID of the component to select, or None to clear selection
        """
        if component_id is not None:
            component_data = self._mock_data_manager.get_component(component_id)
            if component_data is None:
                self._logger.warning("Component %s not found", component_id)
                return

        self._selected_component_id = component_id
        self._mock_data_manager.select_component(component_id)

        # Emit the selected component data
        selected_component = (
            self._mock_data_manager.get_component(component_id)
            if component_id
            else None
        )
        self.component_selected.emit(selected_component)

    def create_component(self, asset_id, **kwargs):
        """Create a new component from an asset with enhanced error handling.

        Args:
            asset_id (str): ID of the asset to create a component from
            **kwargs: Additional attributes to set on the component

        Returns:
            dict: The created component data, or None if creation failed
        """
        try:
            # Validate asset_id
            if not asset_id:
                self._logger.error("create_component: asset_id is None or empty")
                return None

            if not isinstance(asset_id, str):
                self._logger.error(
                    "create_component: asset_id must be string, got %s", type(asset_id)
                )
                return None

            # Check if asset exists
            asset = None
            all_assets = self._mock_data_manager.get_assets()
            for a in all_assets:
                if a.get("id") == asset_id:
                    asset = a
                    break

            if not asset:
                self._logger.error(
                    "create_component: Asset not found for ID: %s", asset_id
                )
                available_ids = [a.get("id") for a in all_assets]
                self._logger.error("Available asset IDs: %s", available_ids)
                return None

            self._logger.info(
                "create_component: Creating component from asset '%s' (ID: %s)",
                asset.get("name", "Unknown"),
                asset_id,
            )

            # Create component using mock data manager
            component_data = self._mock_data_manager.create_component(asset_id)

            if component_data:
                self._logger.info(
                    "create_component: Component created successfully: %s",
                    component_data.get("name", "Unknown"),
                )

                # Select the new component
                self.select_component(component_data["id"])

                # Notify listeners
                self.components_updated.emit(self.get_components())
            else:
                self._logger.error(
                    "create_component: MockDataManager failed to create component"
                )

            return component_data

        except Exception as e:
            self._logger.error(
                "create_component: Unexpected error: %s", str(e), exc_info=True
            )
            return None

    def delete_component(self, component_id):
        """Delete a component by its ID.

        Args:
            component_id (str): ID of the component to delete

        Returns:
            bool: True if the component was deleted, False otherwise
        """
        # Delete using mock data manager
        success = self._mock_data_manager.delete_component(component_id)

        if success:
            # If the deleted component is selected, clear the selection
            if self._selected_component_id == component_id:
                self.select_component(None)

            # Notify listeners
            self.components_updated.emit(self.get_components())

        return success

    def update_component(self, component_id, **kwargs):
        """Update a component's properties.

        Args:
            component_id (str): ID of the component to update
            **kwargs: Attributes to update on the component

        Returns:
            bool: True if the component was updated, False otherwise
        """
        # Update using mock data manager
        success = self._mock_data_manager.update_component(component_id, **kwargs)

        if success:
            # Notify listeners
            self.components_updated.emit(self.get_components())

            # If this is the selected component, emit selection changed
            if component_id == self._selected_component_id:
                component_data = self._mock_data_manager.get_component(component_id)
                self.component_selected.emit(component_data)

        return success

    def save_project(self, filepath):
        """Save the current project to a file.

        Args:
            filepath (str): Path to save the project to

        Returns:
            bool: True if the project was saved successfully, False otherwise
        """
        try:
            # Prepare data for serialization
            project_data = {
                "version": self._current_project.version,
                "metadata": self._current_project.metadata,
                "components": [
                    {
                        "id": comp.id,
                        "asset_id": comp.asset_id,
                        "name": comp.name,
                        "type": comp.type,
                        "parameters": comp.parameters,
                        "transform": comp.transform,
                        "metadata": comp.metadata,
                    }
                    for comp in self._current_project.components
                ],
            }

            # Write to file
            with open(filepath, "w") as f:
                json.dump(project_data, f, indent=4)

            self._logger.info("Project saved to %s", filepath)
            return True

        except Exception as e:
            self._logger.error("Failed to save project: %s", str(e), exc_info=True)
            return False

    def load_project(self, filepath):
        """Load a project from a file.

        Args:
            filepath (str): Path to the project file to load

        Returns:
            bool: True if the project was loaded successfully, False otherwise
        """
        try:
            # Read from file
            with open(filepath, "r") as f:
                project_data = json.load(f)

            # Clear current state
            self._components.clear()
            self._current_project = HairProject()
            self._selected_component_id = None

            # Update project metadata
            self._current_project.version = project_data.get("version", "1.0")
            self._current_project.metadata = project_data.get("metadata", {})

            # Create components
            for comp_data in project_data.get("components", []):
                component = HairComponent(
                    id=comp_data.get("id", str(uuid.uuid4())),
                    asset_id=comp_data["asset_id"],
                    name=comp_data.get("name", "Unnamed Component"),
                    type=comp_data.get("type", "card"),
                    parameters=comp_data.get("parameters", {}),
                    transform=comp_data.get("transform", {}),
                    metadata=comp_data.get("metadata", {}),
                )
                self._components[component.id] = component
                self._current_project.components.append(component)

            # Notify listeners
            self.components_updated.emit(self.get_components())

            self._logger.info("Project loaded from %s", filepath)
            return True

        except Exception as e:
            self._logger.error("Failed to load project: %s", str(e), exc_info=True)
            return False
