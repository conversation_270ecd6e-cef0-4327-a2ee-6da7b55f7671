
"""
毛发工作室UI交互功能测试脚本
用于系统性测试各个UI组件的交互功能
"""

import sys
import os
import logging
import time

# 添加项目路径到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_mock_modules():
    """设置必要的Mock模块"""
    import types
    
    # Mock maya.app.general.mayaMixin
    maya_app = types.ModuleType('maya.app')
    maya_app.general = types.ModuleType('maya.app.general')
    maya_app.general.mayaMixin = types.ModuleType('maya.app.general.mayaMixin')
    
    class MayaQWidgetDockableMixin:
        def setDockableParameters(self, dockable):
            pass
    
    maya_app.general.mayaMixin.MayaQWidgetDockableMixin = MayaQWidgetDockableMixin
    
    sys.modules['maya.app'] = maya_app
    sys.modules['maya.app.general'] = maya_app.general
    sys.modules['maya.app.general.mayaMixin'] = maya_app.general.mayaMixin
    
    # Mock lightbox_ui.banner
    lightbox_ui = sys.modules.get('lightbox_ui')
    if lightbox_ui is None:
        lightbox_ui = types.ModuleType('lightbox_ui')
        sys.modules['lightbox_ui'] = lightbox_ui
    
    if not hasattr(lightbox_ui, 'banner'):
        lightbox_ui.banner = types.ModuleType('lightbox_ui.banner')
        lightbox_ui.banner.setup_banner = lambda *args, **kwargs: None
        sys.modules['lightbox_ui.banner'] = lightbox_ui.banner

def setup_environment():
    """设置测试环境"""
    os.environ['CGAME_AVATAR_FACTORY_RESOURCE'] = os.path.join(project_root, 'cgame_avatar_factory', 'resources')
    os.environ['THM_LOG_LEVEL'] = 'DEBUG'
    
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

class HairStudioUITester:
    """毛发工作室UI交互测试器"""
    
    def __init__(self):
        self.test_results = []
        self.hair_studio_tab = None
        self.main_window = None
        
    def log_test_result(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            'test': test_name,
            'success': success,
            'message': message,
            'status': status
        }
        self.test_results.append(result)
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
    
    def setup_ui(self):
        """设置UI环境"""
        try:
            from qtpy import QtWidgets, QtCore
            from dayu_widgets import dayu_theme
            from dayu_widgets.qt import application
            from cgame_avatar_factory.hair_studio import HairStudioTab
            
            # 创建应用程序
            self.app = QtWidgets.QApplication.instance()
            if self.app is None:
                self.app = QtWidgets.QApplication(sys.argv)
            
            # 设置主题
            dayu_theme.set_theme("dark")
            dayu_theme.set_primary_color(dayu_theme.blue)
            
            # 创建主窗口
            self.main_window = QtWidgets.QMainWindow()
            self.main_window.setWindowTitle("毛发工作室UI交互测试")
            self.main_window.setGeometry(100, 100, 1400, 900)
            
            # 创建毛发工作室tab
            self.hair_studio_tab = HairStudioTab()
            
            # 应用主题
            dayu_theme.apply(self.main_window)
            dayu_theme.apply(self.hair_studio_tab)
            
            # 设置中央widget
            self.main_window.setCentralWidget(self.hair_studio_tab)
            
            self.log_test_result("UI环境设置", True, "主窗口和毛发工作室tab创建成功")
            return True
            
        except Exception as e:
            self.log_test_result("UI环境设置", False, f"设置失败: {str(e)}")
            return False
    
    def test_tab_switching(self):
        """测试tab切换功能"""
        try:
            if not self.hair_studio_tab:
                self.log_test_result("Tab切换测试", False, "毛发工作室tab未初始化")
                return False
            
            # 获取tab数量
            tab_count = self.hair_studio_tab.count()
            self.log_test_result("Tab数量检查", tab_count > 0, f"发现{tab_count}个tab页面")
            
            # 测试切换到每个tab
            for i in range(tab_count):
                try:
                    tab_text = self.hair_studio_tab.tabText(i)
                    self.hair_studio_tab.setCurrentIndex(i)
                    current_index = self.hair_studio_tab.currentIndex()
                    
                    success = current_index == i
                    self.log_test_result(f"切换到{tab_text}tab", success, 
                                       f"期望索引{i}, 实际索引{current_index}")
                    
                    # 获取当前tab的widget
                    current_widget = self.hair_studio_tab.currentWidget()
                    if current_widget:
                        self.log_test_result(f"{tab_text}tab widget检查", True, 
                                           f"Widget类型: {type(current_widget).__name__}")
                    else:
                        self.log_test_result(f"{tab_text}tab widget检查", False, "Widget为None")
                        
                except Exception as e:
                    self.log_test_result(f"Tab{i}切换", False, f"切换失败: {str(e)}")
            
            return True
            
        except Exception as e:
            self.log_test_result("Tab切换测试", False, f"测试失败: {str(e)}")
            return False
    
    def test_asset_library_functionality(self):
        """测试素材库功能"""
        try:
            # 切换到第一个tab进行测试
            self.hair_studio_tab.setCurrentIndex(0)
            current_tab = self.hair_studio_tab.currentWidget()
            
            if not hasattr(current_tab, 'asset_library'):
                self.log_test_result("素材库组件检查", False, "当前tab没有asset_library属性")
                return False
            
            asset_library = current_tab.asset_library
            self.log_test_result("素材库组件检查", True, f"素材库类型: {type(asset_library).__name__}")
            
            # 测试搜索功能
            if hasattr(asset_library, 'search_edit'):
                search_edit = asset_library.search_edit
                
                # 测试搜索输入
                test_search_text = "test"
                search_edit.setText(test_search_text)
                actual_text = search_edit.text()
                
                success = actual_text == test_search_text
                self.log_test_result("素材库搜索输入", success, 
                                   f"输入'{test_search_text}', 实际'{actual_text}'")
                
                # 清空搜索
                search_edit.clear()
                self.log_test_result("素材库搜索清空", search_edit.text() == "", 
                                   f"清空后文本: '{search_edit.text()}'")
            else:
                self.log_test_result("素材库搜索组件", False, "没有找到search_edit组件")
            
            # 测试设置按钮
            if hasattr(asset_library, 'settings_btn'):
                settings_btn = asset_library.settings_btn
                self.log_test_result("素材库设置按钮", True, "设置按钮存在")
                
                # 测试点击设置按钮（这会弹出消息框，我们只测试是否可调用）
                try:
                    # 不实际点击，只检查方法是否存在
                    if hasattr(asset_library, '_on_settings_clicked'):
                        self.log_test_result("设置按钮点击方法", True, "_on_settings_clicked方法存在")
                    else:
                        self.log_test_result("设置按钮点击方法", False, "_on_settings_clicked方法不存在")
                except Exception as e:
                    self.log_test_result("设置按钮功能", False, f"测试失败: {str(e)}")
            else:
                self.log_test_result("素材库设置按钮", False, "没有找到settings_btn组件")
            
            # 测试资产刷新功能
            if hasattr(asset_library, 'refresh'):
                try:
                    asset_library.refresh()
                    self.log_test_result("素材库刷新功能", True, "refresh方法执行成功")
                except Exception as e:
                    self.log_test_result("素材库刷新功能", False, f"refresh执行失败: {str(e)}")
            else:
                self.log_test_result("素材库刷新功能", False, "没有找到refresh方法")
            
            return True
            
        except Exception as e:
            self.log_test_result("素材库功能测试", False, f"测试失败: {str(e)}")
            return False
    
    def test_component_list_functionality(self):
        """测试组件列表功能"""
        try:
            # 使用第一个tab进行测试
            current_tab = self.hair_studio_tab.currentWidget()
            
            if not hasattr(current_tab, 'component_list'):
                self.log_test_result("组件列表检查", False, "当前tab没有component_list属性")
                return False
            
            component_list = current_tab.component_list
            self.log_test_result("组件列表检查", True, f"组件列表类型: {type(component_list).__name__}")
            
            # 测试添加按钮
            if hasattr(component_list, 'add_button'):
                add_button = component_list.add_button
                self.log_test_result("组件列表添加按钮", True, "添加按钮存在")
            else:
                self.log_test_result("组件列表添加按钮", False, "没有找到add_button")
            
            # 测试删除按钮
            if hasattr(component_list, 'remove_button'):
                remove_button = component_list.remove_button
                self.log_test_result("组件列表删除按钮", True, "删除按钮存在")
            else:
                self.log_test_result("组件列表删除按钮", False, "没有找到remove_button")
            
            # 测试组件列表widget
            if hasattr(component_list, 'component_list'):
                list_widget = component_list.component_list
                self.log_test_result("组件列表widget", True, f"列表widget类型: {type(list_widget).__name__}")
                
                # 测试列表操作
                initial_count = list_widget.count() if hasattr(list_widget, 'count') else 0
                self.log_test_result("组件列表初始状态", True, f"初始组件数量: {initial_count}")
                
            else:
                self.log_test_result("组件列表widget", False, "没有找到component_list widget")
            
            # 测试信号
            signals_to_check = ['component_selected', 'component_deleted']
            for signal_name in signals_to_check:
                if hasattr(component_list, signal_name):
                    self.log_test_result(f"组件列表{signal_name}信号", True, f"{signal_name}信号存在")
                else:
                    self.log_test_result(f"组件列表{signal_name}信号", False, f"{signal_name}信号不存在")
            
            return True
            
        except Exception as e:
            self.log_test_result("组件列表功能测试", False, f"测试失败: {str(e)}")
            return False
    
    def test_editor_area_functionality(self):
        """测试编辑区域功能"""
        try:
            current_tab = self.hair_studio_tab.currentWidget()
            
            if not hasattr(current_tab, 'editor_area'):
                self.log_test_result("编辑区域检查", False, "当前tab没有editor_area属性")
                return False
            
            editor_area = current_tab.editor_area
            self.log_test_result("编辑区域检查", True, f"编辑区域类型: {type(editor_area).__name__}")
            
            # 检查编辑区域的基本方法
            methods_to_check = ['set_component', 'clear_component']
            for method_name in methods_to_check:
                if hasattr(editor_area, method_name):
                    self.log_test_result(f"编辑区域{method_name}方法", True, f"{method_name}方法存在")
                else:
                    self.log_test_result(f"编辑区域{method_name}方法", False, f"{method_name}方法不存在")
            
            return True
            
        except Exception as e:
            self.log_test_result("编辑区域功能测试", False, f"测试失败: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("毛发工作室UI交互功能测试")
        print("=" * 60)
        
        # 设置环境
        setup_environment()
        setup_mock_modules()
        
        # 设置UI
        if not self.setup_ui():
            return False
        
        # 显示窗口
        self.main_window.show()
        
        # 运行各项测试
        print("\n开始UI交互测试...")
        
        self.test_tab_switching()
        self.test_asset_library_functionality()
        self.test_component_list_functionality()
        self.test_editor_area_functionality()
        
        # 输出测试结果总结
        self.print_test_summary()
        
        return True
    
    def print_test_summary(self):
        """打印测试结果总结"""
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  ❌ {result['test']}: {result['message']}")
        
        print("\n" + "=" * 60)

def main():
    """主函数"""
    tester = HairStudioUITester()
    
    try:
        success = tester.run_all_tests()
        if success:
            print("测试完成，窗口将保持打开状态以便进一步检查...")
            print("按Ctrl+C退出测试")
            
            # 保持应用运行
            if hasattr(tester, 'app'):
                tester.app.exec_()
        else:
            print("测试设置失败")
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
