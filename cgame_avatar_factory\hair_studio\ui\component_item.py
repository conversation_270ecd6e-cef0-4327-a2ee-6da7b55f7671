"""Component item widget for the Hair Studio component list.

This module provides a custom widget for displaying hair components in the component list
with icons, visibility toggle, and other visual elements.
"""

import logging
from qtpy import QtWidgets, QtCore, QtGui
from qtpy.QtCore import Qt, Signal

from dayu_widgets import <PERSON><PERSON>abe<PERSON>, <PERSON><PERSON>Button

from cgame_avatar_factory.hair_studio.constants import (
    ICON_EYE_LINE,
    ICON_EYE_OFF_LINE,
    ICON_HAIR_CARD,
    ICON_HAIR_XGEN,
    ICON_HAIR_CURVE,
)
from cgame_avatar_factory.hair_studio.utils.icon_utils import (
    set_label_pixmap_with_fallback,
    set_button_icon_with_fallback,
)


class ComponentItem(QtWidgets.QWidget):
    """Custom widget for displaying a hair component in the list.

    This widget shows the component name, asset icon, and visibility toggle.
    """

    # Signals
    clicked = Signal(dict)  # Emitted when the component is clicked
    visibility_toggled = Signal(
        str, bool
    )  # Emitted when visibility is toggled (component_id, is_visible)

    def __init__(self, component_data, parent=None, logger=None):
        """Initialize the ComponentItem.

        Args:
            component_data (dict): The component data
            parent (QWidget, optional): The parent widget
            logger (logging.Logger, optional): Logger instance
        """
        super(ComponentItem, self).__init__(parent)
        self.component_data = component_data
        self._logger = logger if logger is not None else logging.getLogger(__name__)

        # Track selection state
        self._is_selected = False

        # Set up UI
        self.setup_ui()

        # Set initial state
        self._update_visibility_button()
        self._update_style()

    def setup_ui(self):
        """Set up the user interface."""
        # Main horizontal layout
        layout = QtWidgets.QHBoxLayout(self)
        layout.setContentsMargins(4, 2, 4, 2)  # Reduced horizontal margins
        layout.setSpacing(4)  # Reduced spacing for better fit

        # 1. Visibility toggle button (eye icon) - FIRST
        self.visibility_button = MPushButton()
        self.visibility_button.setFixedSize(20, 20)
        self.visibility_button.clicked.connect(self._on_visibility_clicked)
        self._update_visibility_button()

        # 2. Asset type icon - SECOND
        self.asset_icon = MLabel()
        self.asset_icon.setFixedSize(16, 16)
        self.asset_icon.setAlignment(Qt.AlignCenter)  # Center align the icon
        self._set_asset_icon()

        # 3. Component name label - THIRD
        self.name_label = MLabel(self.component_data.get("name", "Unnamed Component"))
        self.name_label.set_elide_mode(Qt.ElideRight)
        self.name_label.setToolTip(self.name_label.text())
        self.name_label.setAlignment(
            Qt.AlignVCenter | Qt.AlignLeft
        )  # Vertical center align

        # Add widgets to layout in correct order: eye -> asset icon -> name
        layout.addWidget(self.visibility_button)
        layout.addWidget(self.asset_icon)
        layout.addWidget(self.name_label, 1)  # Stretch to fill available space

        # Set size policy and height
        self.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        self.setFixedHeight(24)
        self.setMinimumHeight(24)  # Ensure minimum height
        self.setMaximumHeight(24)  # Prevent height expansion

    def _set_asset_icon(self):
        """Set the appropriate asset type icon."""
        asset_type = self.component_data.get("type", "card")

        # Map asset types to icons
        icon_map = {
            "card": ICON_HAIR_CARD,
            "xgen": ICON_HAIR_XGEN,
            "curve": ICON_HAIR_CURVE,
        }

        icon_name = icon_map.get(asset_type, ICON_HAIR_CARD)

        try:
            set_label_pixmap_with_fallback(self.asset_icon, icon_name, 16, 16)
            self._logger.debug("Asset icon set for type %s: %s", asset_type, icon_name)
        except Exception as e:
            self._logger.warning(
                "Failed to set asset icon for %s: %s", asset_type, str(e)
            )
            # Set a simple background color as fallback
            self.asset_icon.setStyleSheet(
                "QLabel { background-color: #4080FF; border: 1px solid #FFFFFF; }"
            )

    def _update_visibility_button(self):
        """Update the visibility button icon based on component state."""
        is_visible = self.component_data.get("is_viewed", True)

        try:
            if is_visible:
                set_button_icon_with_fallback(self.visibility_button, ICON_EYE_LINE)
                self.visibility_button.setToolTip("Hide component")
                self.visibility_button.setText("")  # Clear any text
            else:
                set_button_icon_with_fallback(self.visibility_button, ICON_EYE_OFF_LINE)
                self.visibility_button.setToolTip("Show component")
                self.visibility_button.setText("")  # Clear any text

            self._logger.debug(
                "Visibility button updated: %s", "visible" if is_visible else "hidden"
            )

        except Exception as e:
            self._logger.warning("Failed to set visibility icon: %s", str(e))
            # Fallback to text-based button
            if is_visible:
                self.visibility_button.setText("👁")  # Eye emoji as fallback
                self.visibility_button.setToolTip("Hide component")
            else:
                self.visibility_button.setText("🚫")  # No entry emoji as fallback
                self.visibility_button.setToolTip("Show component")

    def _on_visibility_clicked(self):
        """Handle visibility button click."""
        current_visibility = self.component_data.get("is_viewed", True)
        new_visibility = not current_visibility

        # Update component data
        self.component_data["is_viewed"] = new_visibility

        # Update button
        self._update_visibility_button()

        # Emit signal
        component_id = self.component_data.get("id")
        if component_id:
            self.visibility_toggled.emit(component_id, new_visibility)

        self._logger.debug(
            "Component visibility toggled: %s -> %s",
            self.component_data.get("name", "Unknown"),
            "visible" if new_visibility else "hidden",
        )

    def mousePressEvent(self, event):
        """Handle mouse press events."""
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.component_data)
        super(ComponentItem, self).mousePressEvent(event)

    def set_selected(self, selected):
        """Set the selection state.

        Args:
            selected (bool): True to select, False to deselect
        """
        if self._is_selected != selected:
            self._is_selected = selected
            self._update_style()

    def is_selected(self):
        """Check if this item is selected.

        Returns:
            bool: True if selected, False otherwise
        """
        return self._is_selected

    def _update_style(self):
        """Update the widget style based on selection state."""
        if self._is_selected:
            # Selected state
            self.setStyleSheet(
                """
                ComponentItem {
                    background: rgba(64, 128, 255, 0.2);
                    border: 1px solid #4080FF;
                    border-radius: 2px;
                }
                """
            )
        else:
            # Normal state
            self.setStyleSheet(
                """
                ComponentItem {
                    background: transparent;
                    border: 1px solid transparent;
                    border-radius: 2px;
                }
                ComponentItem:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border: 1px solid #3E3E3E;
                }
                """
            )

    def get_component_data(self):
        """Get the component data.

        Returns:
            dict: The component data
        """
        return self.component_data

    def update_component_data(self, component_data):
        """Update the component data and refresh the display.

        Args:
            component_data (dict): The new component data
        """
        self.component_data = component_data

        # Update display elements
        self.name_label.setText(component_data.get("name", "Unnamed Component"))
        self.name_label.setToolTip(self.name_label.text())
        self._set_asset_icon()
        self._update_visibility_button()
