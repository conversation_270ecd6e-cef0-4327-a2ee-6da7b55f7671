import pytest
from qtpy import QtC<PERSON>


def test_tab_switching_updates_asset_library(hair_studio_widget, qtbot):
    """Verify that switching tabs correctly shows/hides the corresponding asset library."""
    # The main widget is a QTabWidget. Its pages are the BaseHairTab instances.
    # When a tab is not selected, its page widget is hidden.

    # Initially, the Card tab (index 0) should be active.
    assert hair_studio_widget.currentIndex() == 0
    assert hair_studio_widget.widget(0).isVisible()
    assert not hair_studio_widget.widget(1).isVisible()
    assert not hair_studio_widget.widget(2).isVisible()

    # Therefore, only the card tab's asset library should be visible.
    assert hair_studio_widget.card_tab.asset_library.isVisible()
    assert not hair_studio_widget.xgen_tab.asset_library.isVisible()
    assert not hair_studio_widget.curve_tab.asset_library.isVisible()

    # Simulate clicking on the XGen tab (index 1).
    qtbot.mouseClick(
        hair_studio_widget.tabBar(),
        QtCore.Qt.LeftButton,
        pos=hair_studio_widget.tabBar().tabRect(1).center(),
    )

    # Now, the XGen tab should be active and its library visible.
    assert hair_studio_widget.currentIndex() == 1
    assert not hair_studio_widget.card_tab.asset_library.isVisible()
    assert hair_studio_widget.xgen_tab.asset_library.isVisible()
    assert not hair_studio_widget.curve_tab.asset_library.isVisible()

    # Simulate clicking on the Curve tab (index 2).
    qtbot.mouseClick(
        hair_studio_widget.tabBar(),
        QtCore.Qt.LeftButton,
        pos=hair_studio_widget.tabBar().tabRect(2).center(),
    )

    # Finally, the Curve tab should be active and its library visible.
    assert hair_studio_widget.currentIndex() == 2
    assert not hair_studio_widget.card_tab.asset_library.isVisible()
    assert not hair_studio_widget.xgen_tab.asset_library.isVisible()
    assert hair_studio_widget.curve_tab.asset_library.isVisible()
