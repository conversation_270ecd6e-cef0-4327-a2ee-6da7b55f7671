#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的日志管理测试
只测试数据层和管理层的logger传递，避免UI依赖
"""

import sys
import os
import logging
import io

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def test_logging_simple():
    """简单的日志管理测试"""
    print("=" * 60)
    print("毛发工作室日志管理简单测试")
    print("=" * 60)

    try:
        # 创建一个自定义logger，模拟主窗口的logger
        main_logger = logging.getLogger("test_main_window")
        main_logger.setLevel(logging.DEBUG)

        # 创建一个StringIO来捕获日志输出
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter("%(name)s - %(levelname)s - %(message)s")
        handler.setFormatter(formatter)
        main_logger.addHandler(handler)

        print("✓ 主窗口logger设置完成")

        # 测试1: 直接导入模拟数据管理器（避免通过__init__.py）
        print("\n1. 测试模拟数据管理器...")
        sys.path.insert(
            0, os.path.join(project_root, "cgame_avatar_factory", "hair_studio", "data")
        )
        from mock_data_manager import MockDataManager

        mock_manager = MockDataManager()
        components = mock_manager.get_components()
        print(f"   ✓ 模拟数据管理器工作正常，获取到 {len(components)} 个组件")

        # 测试2: 测试HairManager的logger传递（直接导入避免UI依赖）
        print("\n2. 测试HairManager的logger传递...")
        sys.path.insert(
            0,
            os.path.join(
                project_root, "cgame_avatar_factory", "hair_studio", "manager"
            ),
        )

        # 需要先Mock QtCore
        import types

        class MockQObject:
            def __init__(self, parent=None):
                pass

        class MockSignal:
            def __init__(self, *args):
                pass

            def emit(self, *args):
                pass

            def connect(self, func):
                pass

        qtcore_mock = types.ModuleType("qtpy.QtCore")
        qtcore_mock.QObject = MockQObject
        qtcore_mock.Signal = MockSignal
        sys.modules["qtpy.QtCore"] = qtcore_mock
        sys.modules["qtpy"] = types.ModuleType("qtpy")
        sys.modules["qtpy"].QtCore = qtcore_mock

        from hair_manager import HairManager

        # 使用主logger创建HairManager
        hair_manager = HairManager(logger=main_logger)

        # 触发一些日志记录
        hair_manager._logger.info("HairManager初始化完成")
        hair_manager._logger.debug("测试debug日志")

        # 测试组件操作
        assets = hair_manager.get_assets()
        if assets:
            component = hair_manager.create_component(assets[0]["id"])
            if component:
                hair_manager._logger.info("成功创建组件: %s", component["name"])

        print("   ✓ HairManager logger传递正常")

        # 检查日志输出
        print("\n3. 检查日志输出...")
        log_output = log_stream.getvalue()
        log_lines = [line for line in log_output.strip().split("\n") if line.strip()]

        print(f"   ✓ 捕获到 {len(log_lines)} 条日志记录")

        # 验证所有组件都使用了同一个logger名称
        expected_logger_name = "test_main_window"
        for line in log_lines:
            if line.strip():  # 跳过空行
                logger_name = line.split(" - ")[0]
                if logger_name != expected_logger_name:
                    print(f"   ✗ 发现不一致的logger名称: {logger_name}")
                    return False

        print(f"   ✓ 所有组件都使用了统一的logger: {expected_logger_name}")

        # 显示日志内容
        print("\n4. 日志输出示例:")
        for i, line in enumerate(log_lines):
            if line.strip():
                print(f"   {i+1}. {line}")

        # 测试logger层次结构
        print("\n5. 测试logger层次结构...")

        # 创建子logger
        child_logger = main_logger.getChild("hair_manager")
        child_logger.info("子logger测试")

        # 再次检查日志
        log_output = log_stream.getvalue()
        new_lines = [line for line in log_output.strip().split("\n") if line.strip()]

        if len(new_lines) > len(log_lines):
            last_line = new_lines[-1]
            print(f"   ✓ 子logger工作正常: {last_line}")

        print("\n" + "=" * 60)
        print("日志管理简单测试完成！")
        print("✓ 所有测试通过")
        print("✓ Logger传递机制正常工作")
        print("✓ 模拟数据管理器正常工作")
        print("✓ HairManager logger传递正常")
        print("✓ 日志输出格式正确")
        print("\n实现的功能:")
        print("1. ✓ 统一的logger传递机制")
        print("2. ✓ 所有组件使用同一个logger实例")
        print("3. ✓ 日志格式统一")
        print("4. ✓ 支持logger层次结构")
        print("=" * 60)

        return True

    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_logging_simple()
    if success:
        print("\n✓ 日志管理简单测试成功")
    else:
        print("\n✗ 日志管理简单测试失败")
        sys.exit(1)
