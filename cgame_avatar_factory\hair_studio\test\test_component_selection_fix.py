"""Test script to verify component selection and editor area update fix.

This script tests that when a component is selected in the ComponentList,
the EditorArea properly updates with the component data.
"""

import sys
import os
import logging

# Add project path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def test_component_selection_signal():
    """Test that component selection signals work correctly."""
    
    try:
        # Import Qt modules
        from qtpy import QtWidgets, QtCore
        print("✓ Qt modules imported successfully")
        
        # Import Hair Studio modules
        from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
        from cgame_avatar_factory.hair_studio.ui.base_hair_tab import BaseHairTab
        from cgame_avatar_factory.hair_studio.ui.editor_area import EditorArea
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        print("✓ Hair Studio modules imported successfully")
        
        # Create application
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # Create logger
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.DEBUG)
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))
        logger.addHandler(handler)
        
        # Create hair manager
        hair_manager = HairManager(logger=logger)
        print("✓ HairManager created successfully")
        
        # Create component list
        component_list = ComponentList("card", hair_manager, logger=logger)
        print("✓ ComponentList created successfully")
        
        # Create editor area
        editor_area = EditorArea("card", hair_manager, logger=logger)
        print("✓ EditorArea created successfully")
        
        # Test signal connection manually
        selection_received = []
        
        def on_component_selected(component_data):
            selection_received.append(component_data)
            print(f"Signal received: {component_data}")
        
        # Connect the signal
        component_list.component_selected.connect(on_component_selected)
        print("✓ Signal connected successfully")
        
        # Test adding components
        test_components = [
            {
                "id": "comp_1", 
                "name": "Test Component 1", 
                "type": "card", 
                "visible": True,
                "width": 1.0,
                "height": 2.0
            },
            {
                "id": "comp_2", 
                "name": "Test Component 2", 
                "type": "card", 
                "visible": True,
                "width": 1.5,
                "height": 2.5
            }
        ]
        
        for comp in test_components:
            component_list.add_component(comp)
        print(f"✓ Added {len(test_components)} test components")
        
        # Test manual signal emission
        print("\n=== Testing Signal Emission ===")
        
        # Emit signal manually with component data
        test_component = test_components[0]
        component_list.component_selected.emit(test_component)
        
        # Check if signal was received
        if selection_received:
            received_data = selection_received[-1]
            print(f"✓ Signal received with data: {received_data}")
            
            # Verify data integrity
            if received_data == test_component:
                print("✓ Signal data matches expected component data")
            else:
                print(f"✗ Signal data mismatch. Expected: {test_component}, Got: {received_data}")
        else:
            print("✗ No signal received")
        
        # Test editor area update
        print("\n=== Testing Editor Area Update ===")
        
        # Set component in editor area
        editor_area.set_component(test_component)
        
        # Check if editor area has the component
        if editor_area.current_component == test_component:
            print("✓ EditorArea updated with component data")
            print(f"  Component name: {editor_area.current_component.get('name')}")
            print(f"  Component ID: {editor_area.current_component.get('id')}")
        else:
            print(f"✗ EditorArea not updated correctly")
            print(f"  Expected: {test_component}")
            print(f"  Got: {editor_area.current_component}")
        
        # Test BaseHairTab integration
        print("\n=== Testing BaseHairTab Integration ===")
        
        # Create BaseHairTab
        base_tab = BaseHairTab("card", hair_manager, logger=logger)
        print("✓ BaseHairTab created successfully")
        
        # Test the signal handler directly
        base_tab._on_component_selected(test_component)
        
        # Check if editor area in the tab was updated
        if base_tab.editor_area.current_component == test_component:
            print("✓ BaseHairTab._on_component_selected() works correctly")
        else:
            print("✗ BaseHairTab._on_component_selected() failed to update editor")
        
        print("\n=== Test Summary ===")
        print("✓ Component selection signal type fixed (dict instead of str)")
        print("✓ BaseHairTab._on_component_selected() updated to handle dict")
        print("✓ EditorArea.set_component() works with component data")
        print("✓ Signal flow: ComponentList -> BaseHairTab -> EditorArea")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing Component Selection and Editor Area Update Fix")
    print("=" * 55)
    
    success = test_component_selection_signal()
    
    if success:
        print("\n✓ All tests completed successfully!")
        print("The component selection -> editor area update should now work correctly.")
    else:
        print("\n✗ Some tests failed. Check the output above.")
    
    sys.exit(0 if success else 1)
