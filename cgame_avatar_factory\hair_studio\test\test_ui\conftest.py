"""Pytest configuration for UI tests."""

import sys
from pathlib import Path
from unittest.mock import MagicMock

# Add the project root to the Python path
project_root = Path(__file__).resolve().parents[5]  # Go up 5 levels to reach the project root
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

import pytest
from qtpy import QtWidgets, QtCore, QtGui
from dayu_widgets import dayu_theme

# Import the widgets to test
from cgame_avatar_factory.hair_studio.ui.hair_studio_tab import HairStudioTab
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager


@pytest.fixture(scope='session')
def qapp_instance():
    """Create a QApplication instance.
    
    This fixture is session-scoped, so it will only be created once per test session.
    """
    app = QtWidgets.QApplication.instance()
    if app is None:
        app = QtWidgets.QApplication(sys.argv)
        dayu_theme.apply(app)
    return app


@pytest.fixture
def qt_logger():
    """Create a mock logger for testing."""
    return MagicMock()


@pytest.fixture
def mock_hair_manager(qt_logger):
    """Create a mock HairManager for testing."""
    manager = MagicMock(spec=HairManager)
    manager.get_assets.return_value = [
        {"id": "asset1", "name": "Test Asset 1", "type": "card"},
        {"id": "asset2", "name": "Test Asset 2", "type": "card"},
        {"id": "asset3", "name": "XGen Hair 1", "type": "xgen"},
    ]
    return manager


@pytest.fixture
def hair_studio_widget(qapp_instance, qtbot, mock_hair_manager, qt_logger):
    """Create and return a HairStudioTab instance for testing.
    
    This fixture is function-scoped, so a new instance will be created for each test.
    """
    widget = HairStudioTab(logger=qt_logger)
    # Replace the hair manager with our mock
    widget._hair_manager = mock_hair_manager
    widget.refresh_tabs()
    
    qtbot.addWidget(widget)
    widget.show()
    return widget


@pytest.fixture
def asset_library(qapp_instance, qtbot, mock_hair_manager, qt_logger):
    """Create and return an AssetLibrary instance for testing."""
    from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
    from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
    
    library = AssetLibrary(
        hair_type=HAIR_TYPE_CARD,
        hair_manager=mock_hair_manager,
        parent=None,
        logger=qt_logger
    )
    qtbot.addWidget(library)
    library.show()
    return library


@pytest.fixture
def component_list(qapp_instance, qtbot, qt_logger):
    """Create and return a ComponentList instance for testing."""
    from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
    from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
    
    component_list = ComponentList(hair_type=HAIR_TYPE_CARD, parent=None, logger=qt_logger)
    qtbot.addWidget(component_list)
    component_list.show()
    return component_list


@pytest.fixture
def editor_area(qapp_instance, qtbot, qt_logger):
    """Create and return an EditorArea instance for testing."""
    from cgame_avatar_factory.hair_studio.ui.editor_area import EditorArea
    
    editor = EditorArea(parent=None, logger=qt_logger)
    qtbot.addWidget(editor)
    editor.show()
    return editor
