"""Test script to verify ComponentList MListView compatibility fixes.

This script tests the fixed methods that were causing AttributeError with MListView.
"""

import sys
import os
import logging

# Add project path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def test_component_list_fixes():
    """Test the ComponentList fixes for MListView compatibility."""
    
    try:
        # Import Qt modules
        from qtpy import QtWidgets, QtCore
        print("✓ Qt modules imported successfully")
        
        # Import Hair Studio modules
        from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        print("✓ Hair Studio modules imported successfully")
        
        # Create application
        app = QtWidgets.QApplication.instance()
        if app is None:
            app = QtWidgets.QApplication(sys.argv)
        
        # Create logger
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.DEBUG)
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter('%(levelname)s: %(message)s'))
        logger.addHandler(handler)
        
        # Create hair manager
        hair_manager = HairManager(logger=logger)
        print("✓ HairManager created successfully")
        
        # Create component list
        component_list = ComponentList("card", hair_manager, logger=logger)
        print("✓ ComponentList created successfully")
        
        # Test adding some components
        test_components = [
            {"id": "comp_1", "name": "Test Component 1", "type": "card", "visible": True},
            {"id": "comp_2", "name": "Test Component 2", "type": "card", "visible": True},
            {"id": "comp_3", "name": "Test Component 3", "type": "card", "visible": True}
        ]
        
        for comp in test_components:
            component_list.add_component(comp)
        print("✓ Test components added successfully")
        
        # Test the fixed methods
        print("\n=== Testing Fixed Methods ===")
        
        # Test get_selected_components
        try:
            selected = component_list.get_selected_components()
            print(f"✓ get_selected_components(): {len(selected)} components")
        except Exception as e:
            print(f"✗ get_selected_components() failed: {e}")
        
        # Test get_component_order
        try:
            order = component_list.get_component_order()
            print(f"✓ get_component_order(): {len(order)} components")
        except Exception as e:
            print(f"✗ get_component_order() failed: {e}")
        
        # Test context menu (simulate right-click)
        try:
            # This tests the count() method fix
            component_list._show_context_menu(QtCore.QPoint(10, 10))
            print("✓ _show_context_menu() executed without errors")
        except Exception as e:
            print(f"✗ _show_context_menu() failed: {e}")
        
        # Test keyboard shortcuts (these would normally be triggered by events)
        try:
            component_list._rename_selected_component()
            print("✓ _rename_selected_component() executed without errors")
        except Exception as e:
            print(f"✗ _rename_selected_component() failed: {e}")
        
        try:
            component_list._duplicate_selected_component()
            print("✓ _duplicate_selected_component() executed without errors")
        except Exception as e:
            print(f"✗ _duplicate_selected_component() failed: {e}")
        
        try:
            component_list._toggle_selected_component_visibility()
            print("✓ _toggle_selected_component_visibility() executed without errors")
        except Exception as e:
            print(f"✗ _toggle_selected_component_visibility() failed: {e}")
        
        try:
            component_list._delete_selected_components()
            print("✓ _delete_selected_components() executed without errors")
        except Exception as e:
            print(f"✗ _delete_selected_components() failed: {e}")
        
        # Test reorder_components
        try:
            current_order = component_list.get_component_order()
            if len(current_order) > 1:
                # Reverse the order
                new_order = current_order[::-1]
                component_list.reorder_components(new_order)
                print("✓ reorder_components() executed without errors")
            else:
                print("✓ reorder_components() skipped (not enough components)")
        except Exception as e:
            print(f"✗ reorder_components() failed: {e}")
        
        print("\n=== Test Summary ===")
        print("All ComponentList MListView compatibility fixes have been tested.")
        print("The methods should now work correctly with both MListView and QListWidget.")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing ComponentList MListView Compatibility Fixes")
    print("=" * 50)
    
    success = test_component_list_fixes()
    
    if success:
        print("\n✓ All tests completed successfully!")
    else:
        print("\n✗ Some tests failed. Check the output above.")
    
    sys.exit(0 if success else 1)
