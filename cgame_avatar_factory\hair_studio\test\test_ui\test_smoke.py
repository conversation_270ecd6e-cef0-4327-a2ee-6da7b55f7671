import pytest

# Import local modules
from cgame_avatar_factory.hair_studio.constants import (
    UI_TEXT_CARD_TAB,
    UI_TEXT_XGEN_TAB,
    UI_TEXT_CURVE_TAB,
)
from qtpy import QtWidgets

def test_hair_studio_loads_and_is_visible(hair_studio_widget):
    """A simple smoke test to ensure the Hair Studio UI loads without crashing."""
    assert hair_studio_widget is not None, 'Hair studio widget should be created.'
    assert hair_studio_widget.isVisible(), 'Hair studio widget should be visible.'

def test_hair_studio_has_correct_tabs(hair_studio_widget):
    """Verify that the main tab widget has the correct tabs."""
    # The hair_studio_widget is the QTabWidget itself.
    assert hair_studio_widget.count() == 3
    assert hair_studio_widget.tabText(0) == UI_TEXT_CARD_TAB
    assert hair_studio_widget.tabText(1) == UI_TEXT_XGEN_TAB
    assert hair_studio_widget.tabText(2) == UI_TEXT_CURVE_TAB
