## 项目背景
    lightbox内部包
### 开发环境
    maya+pyqt的工具开发；maya版本支持2022和2023；
    可以使用dayu_widget组件，源码路径为D:\_thm\rez_local_cache\ext\dayu_widgets\0.13.15\python-3\site-packages\dayu_widgets
    使用rez打包，属于内部项目，package里面可查看引用包；请尽量使用引用包，如有外部引用需要，额外提出并说明原因；

### UI开发指南
UI开发指南：E:\AI-Memory\# Maya + PyQt + dayu_widget 开发指南.md

## 语言要求
1. 代码及其注释使用英文；（涉及需要提交到库上的都使用英文）
2. 与我回复对话框, 文档总结等使用中文；

## AI每次必读
### 回复准则
1. 每次回答结束时，需要简要总结你的操作，重点提出不确定或者没有解决的问题；
2. 每次回答结束时，明确哪些是修改的功能，哪些文件只是测试文件，是否需要删除；

### 已有的测试调试流程
 毛发工作室开发环境使用指南：C:\TAHub\bs_merge\cgame_avatar_factory\.ai\毛发工作室开发环境使用指南.md
