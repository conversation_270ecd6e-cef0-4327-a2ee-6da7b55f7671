"""Tests for the AssetLibrary component."""

import pytest
from qtpy import Qt<PERSON>ore, QtWidgets
from unittest.mock import MagicMock, patch

# Import local modules
from cgame_avatar_factory.hair_studio.constants import (
    HAIR_TYPE_CARD,
    HAIR_TYPE_XGEN,
    HAIR_TYPE_CURVE,
    UI_TEXT_SEARCH_PLACEHOLDER,
)
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_item import AssetItem


class TestAssetLibrary:
    """Test cases for the AssetLibrary component."""

    @pytest.fixture
    def mock_hair_manager(self):
        """Create a mock HairManager."""
        manager = MagicMock(spec=HairManager)
        manager.get_assets.return_value = [
            {"id": "asset1", "name": "Test Asset 1", "type": "card"},
            {"id": "asset2", "name": "Test Asset 2", "type": "card"},
            {"id": "asset3", "name": "XGen Hair 1", "type": "xgen"},
        ]
        return manager

    @pytest.fixture
    def asset_library(self, mock_hair_manager, qapp_instance, qtbot):
        """Create an AssetLibrary instance for testing."""
        library = AssetLibrary(
            hair_type=HAIR_TYPE_CARD,
            hair_manager=mock_hair_manager,
            parent=None,
        )
        qtbot.addWidget(library)
        library.show()
        return library

    def test_initialization(self, asset_library):
        """Test that the asset library initializes correctly."""
        assert asset_library is not None
        assert asset_library.hair_type == HAIR_TYPE_CARD
        assert asset_library.search_edit.placeholderText() == UI_TEXT_SEARCH_PLACEHOLDER

    def test_refresh_loads_assets(self, asset_library, mock_hair_manager):
        """Test that refresh() loads assets from the hair manager."""
        # Clear the mock call count
        mock_hair_manager.get_assets.reset_mock()
        
        # Call refresh
        asset_library.refresh()
        
        # Verify get_assets was called with the correct hair type
        # Note: Using assert_any_call instead of assert_called_once_with to be more flexible with argument passing
        mock_hair_manager.get_assets.assert_any_call(asset_type=HAIR_TYPE_CARD)
        
        # Verify assets were added to the layout
        assert asset_library.assets_layout.count() > 0

    def test_search_filters_assets(self, asset_library, qtbot):
        """Test that the search filters assets correctly."""
        # Initial assets should be loaded
        initial_count = asset_library.assets_layout.count()
        assert initial_count > 0
        
        # Search for a specific asset
        with qtbot.waitSignal(asset_library.search_edit.textChanged, timeout=1000):
            asset_library.search_edit.setText("Test")
            
        # Should have fewer or equal items after search
        assert asset_library.assets_layout.count() <= initial_count
        
        # Clear search
        with qtbot.waitSignal(asset_library.search_edit.textChanged, timeout=1000):
            asset_library.search_edit.clear()
            
        # Should return to original count
        assert asset_library.assets_layout.count() == initial_count

    def test_asset_selection(self, asset_library, qtbot):
        """Test that selecting an asset emits the correct signal."""
        # Get the first asset item in the library
        # assets_layout is a QGridLayout, so we need to get the item at (0, 0)
        item = asset_library.assets_layout.itemAtPosition(0, 0)
        if not item:
            # If no item at (0,0), try to find the first valid item
            for i in range(asset_library.assets_layout.count()):
                item = asset_library.assets_layout.itemAt(i)
                if item and item.widget():
                    break
        
        # Skip test if no items found
        if not item or not item.widget():
            pytest.skip("No asset items found in the library")
            
        asset_item = item.widget()
        
        # Connect to the asset_selected signal
        with qtbot.waitSignal(asset_library.asset_selected, timeout=1000) as blocker:
            # Simulate clicking the asset item
            qtbot.mouseClick(asset_item, QtCore.Qt.LeftButton)
            
        # Verify the correct asset data was emitted
        assert blocker.args[0] == asset_item.asset_data
        assert blocker.args[0]["id"] == "asset1"
        assert blocker.args[0]["name"] == "Test Asset 1"

    @pytest.mark.parametrize("hair_type", [HAIR_TYPE_CARD, HAIR_TYPE_XGEN, HAIR_TYPE_CURVE])
    def test_different_hair_types(self, hair_type, mock_hair_manager, qapp_instance, qtbot):
        """Test that the asset library works with different hair types."""
        library = AssetLibrary(
            hair_type=hair_type,
            hair_manager=mock_hair_manager,
            parent=None,
        )
        qtbot.addWidget(library)
        
        # Verify the library was created with the correct hair type
        assert library.hair_type == hair_type
        
        # Verify it loads assets for the correct hair type
        # Using assert_any_call with named parameter to match the actual call
        mock_hair_manager.get_assets.assert_any_call(asset_type=hair_type)
        # Verify that assets were added to the layout
        assert library.assets_layout.count() > 0
