"""Tests for the ComponentList component."""

import pytest
from qtpy import Qt<PERSON><PERSON>, QtWidgets
from unittest.mock import MagicMock, patch

# Import local modules
from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList


class TestComponentList:
    """Test cases for the ComponentList component."""

    @pytest.fixture
    def component_list(self, qapp_instance, qtbot):
        """Create a ComponentList instance for testing."""
        component_list = ComponentList(parent=None)
        qtbot.addWidget(component_list)
        component_list.show()
        return component_list

    def test_initialization(self, component_list):
        """Test that the component list initializes correctly."""
        assert component_list is not None
        assert component_list.list_widget is not None
        assert component_list.list_widget.count() == 0

    def test_add_component(self, component_list):
        """Test adding a component to the list."""
        # Add a test component
        component_data = {"id": "comp1", "name": "Test Component", "type": "card"}
        component_list.add_component(component_data)

        # Verify the component was added
        assert component_list.list_widget.count() == 1
        item = component_list.list_widget.item(0)
        assert item.text() == "Test Component"
        assert item.data(QtCore.Qt.UserRole) == component_data

    def test_remove_component(self, component_list):
        """Test removing a component from the list."""
        # Add a test component
        component_data = {"id": "comp1", "name": "Test Component", "type": "card"}
        component_list.add_component(component_data)
        
        # Remove the component
        component_list.remove_component("comp1")
        
        # Verify the component was removed
        assert component_list.list_widget.count() == 0

    def test_clear_components(self, component_list):
        """Test clearing all components from the list."""
        # Add some test components
        for i in range(3):
            component_list.add_component({"id": f"comp{i}", "name": f"Component {i}", "type": "card"})
        
        # Verify components were added
        assert component_list.list_widget.count() == 3
        
        # Clear all components
        component_list.clear_components()
        
        # Verify all components were removed
        assert component_list.list_widget.count() == 0

    def test_get_selected_component(self, component_list, qtbot):
        """Test getting the currently selected component."""
        # Add some test components
        components = [
            {"id": "comp1", "name": "Component 1", "type": "card"},
            {"id": "comp2", "name": "Component 2", "type": "card"},
        ]
        
        for comp in components:
            component_list.add_component(comp)
        
        # Select the first component
        component_list.list_widget.setCurrentRow(0)
        selected = component_list.get_selected_component()
        assert selected == components[0]
        
        # Select the second component
        component_list.list_widget.setCurrentRow(1)
        selected = component_list.get_selected_component()
        assert selected == components[1]

    def test_component_selection_signal(self, component_list, qtbot):
        """Test that selecting a component emits the component_selected signal."""
        # Add a test component
        component_data = {"id": "comp1", "name": "Test Component", "type": "card"}
        component_list.add_component(component_data)
        
        # Connect to the signal
        with qtbot.waitSignal(component_list.component_selected, timeout=1000) as blocker:
            # Select the component
            component_list.list_widget.setCurrentRow(0)
        
        # Verify the signal was emitted with the correct data
        assert blocker.args[0] == component_data

    def test_remove_nonexistent_component(self, component_list):
        """Test that removing a non-existent component doesn't raise an error."""
        # This should not raise an exception
        component_list.remove_component("nonexistent_id")
        
        # The list should still be empty
        assert component_list.list_widget.count() == 0
