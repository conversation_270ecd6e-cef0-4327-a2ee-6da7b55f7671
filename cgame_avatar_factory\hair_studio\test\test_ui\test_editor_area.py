"""Tests for the EditorArea component."""

import pytest
from qtpy import Qt<PERSON>ore, QtWidgets
from unittest.mock import MagicMock, patch

# Import local modules
from cgame_avatar_factory.hair_studio.ui.editor_area import EditorArea


class TestEditorArea:
    """Test cases for the EditorArea component."""

    @pytest.fixture
    def editor_area(self, qapp_instance, qtbot):
        """Create an EditorArea instance for testing."""
        editor = EditorArea(parent=None)
        qtbot.addWidget(editor)
        editor.show()
        return editor

    def test_initialization(self, editor_area):
        """Test that the editor area initializes correctly."""
        assert editor_area is not None
        assert editor_area.scroll_area is not None
        assert editor_area.main_layout is not None
        assert editor_area.properties_form is not None

    def test_load_component(self, editor_area):
        """Test loading a component into the editor."""
        # Define a test component with some properties
        component_data = {
            "id": "comp1",
            "name": "Test Component",
            "type": "card",
            "position": {"x": 0, "y": 0, "z": 0},
            "rotation": {"x": 0, "y": 0, "z": 0},
            "scale": 1.0,
            "visible": True
        }
        
        # Load the component
        editor_area.load_component(component_data)
        
        # Verify the component was loaded
        assert editor_area.current_component == component_data
        
        # Verify the property form was populated
        assert editor_area.properties_form.rowCount() > 0

    def test_clear_editor(self, editor_area):
        """Test clearing the editor."""
        # Load a test component first
        component_data = {"id": "comp1", "name": "Test Component", "type": "card"}
        editor_area.load_component(component_data)
        
        # Clear the editor
        editor_area.clear_editor()
        
        # Verify the editor was cleared
        assert editor_area.current_component is None
        assert editor_area.properties_form.rowCount() == 0

    def test_property_changed_signal(self, editor_area, qtbot):
        """Test that changing a property emits the property_changed signal."""
        # Load a test component
        component_data = {
            "id": "comp1",
            "name": "Test Component",
            "type": "card",
            "scale": 1.0
        }
        editor_area.load_component(component_data)
        
        # Connect to the signal
        with qtbot.waitSignal(editor_area.property_changed, timeout=1000) as blocker:
            # Find the scale widget and change its value
            for i in range(editor_area.properties_form.rowCount()):
                label = editor_area.properties_form.itemAt(i, QtWidgets.QFormLayout.LabelRole).widget()
                if label.text() == "scale":
                    editor = editor_area.properties_form.itemAt(i, QtWidgets.QFormLayout.FieldRole).widget()
                    editor.setValue(1.5)
                    break
        
        # Verify the signal was emitted with the correct data
        assert blocker.args[0] == "scale"
        assert blocker.args[1] == 1.5

    def test_update_component_property(self, editor_area):
        """Test updating a component property."""
        # Load a test component
        component_data = {
            "id": "comp1",
            "name": "Test Component",
            "type": "card",
            "scale": 1.0
        }
        editor_area.load_component(component_data)
        
        # Update a property
        editor_area.update_component_property("scale", 1.5)
        
        # Verify the component data was updated
        assert editor_area.current_component["scale"] == 1.5

    def test_complex_property_editing(self, editor_area, qtbot):
        """Test editing complex properties like position and rotation."""
        # Load a test component with complex properties
        component_data = {
            "id": "comp1",
            "name": "Test Component",
            "type": "card",
            "position": {"x": 0, "y": 0, "z": 0},
            "rotation": {"x": 0, "y": 0, "z": 0}
        }
        editor_area.load_component(component_data)
        
        # Find and update the x position
        with qtbot.waitSignal(editor_area.property_changed, timeout=1000):
            for i in range(editor_area.properties_form.rowCount()):
                label = editor_area.properties_form.itemAt(i, QtWidgets.QFormLayout.LabelRole).widget()
                if label.text() == "position":
                    editor = editor_area.properties_form.itemAt(i, QtWidgets.QFormLayout.FieldRole).widget()
                    # Simulate changing the x value to 10
                    editor.findChild(QtWidgets.QDoubleSpinBox, "x").setValue(10.0)
                    break
        
        # Verify the component data was updated
        assert editor_area.current_component["position"]["x"] == 10.0
