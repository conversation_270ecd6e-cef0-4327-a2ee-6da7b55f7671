"""Component List Module.

This module provides the component list widget for the Hair Studio tool.
It displays the list of hair components that have been added to the scene.
"""

# Import standard library
import os
import logging
import json

# Import Qt modules
from qtpy import QtWidgets, QtCore, QtGui
from qtpy.QtCore import Qt
from qtpy.QtGui import QStandardItem, QStandardItemModel

# Import dayu widgets
from dayu_widgets import <PERSON><PERSON>abel, MToolButton, MFlowLayout, MListView

# Import local modules
from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
from cgame_avatar_factory.hair_studio.ui.component_item import ComponentItem
from cgame_avatar_factory.hair_studio.constants import (
    UI_TEXT_HAIR_COMPONENT_LIST,
    LAYOUT_MARGIN_ZERO,
    LAYOUT_SPACING_SMALL,
    OBJECT_NAME_COMPONENT_LIST_FORMAT,
    ICON_ADD_LINE,
    ICON_TRASH_LINE,
)
from cgame_avatar_factory.hair_studio.utils.icon_utils import (
    get_icon_with_fallback,
    set_button_icon_with_fallback,
)


class ComponentList(QtWidgets.QWidget):
    """Component List Widget.

    This widget displays a list of hair components that have been added to the scene.
    Users can select components to edit their properties in the editor area.
    """

    # Signal emitted when a component is selected
    component_selected = QtCore.Signal(dict)

    # Signal emitted when a component is deleted
    component_deleted = QtCore.Signal(str)

    # Signal emitted when components are reordered
    components_reordered = QtCore.Signal(list)

    # Signal emitted when component is renamed
    component_renamed = QtCore.Signal(str, str)  # component_id, new_name

    # Signal emitted when component visibility is toggled
    component_visibility_toggled = QtCore.Signal(str, bool)  # component_id, visible

    def __init__(self, hair_type, hair_manager=None, parent=None, logger=None):
        """Initialize the ComponentList.

        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager, optional): The hair manager instance. If None, a new one will be created.
            parent (QWidget, optional): The parent widget. Defaults to None.
            logger (logging.Logger, optional): Logger instance to use. If None, creates a new one.
        """
        super(ComponentList, self).__init__(parent)
        self.hair_type = hair_type
        self.object_name = "{}ComponentList".format(hair_type.capitalize())
        self.setObjectName(self.object_name)

        # Initialize logger - use provided logger or create new one
        self._logger = logger if logger is not None else logging.getLogger(__name__)

        # Initialize manager
        self.manager = (
            hair_manager
            if hair_manager is not None
            else HairManager(logger=self._logger)
        )

        # Initialize state variables for enhanced interactions
        self._drag_start_position = None
        self._is_dragging = False
        self._multi_selection_enabled = True
        self._reorder_enabled = True

        # Initialize UI
        self.setup_ui()

        # Enable drop operations and enhanced interactions
        self.setAcceptDrops(True)
        self._setup_enhanced_interactions()

    def setup_ui(self):
        """Set up the user interface components."""
        # Main layout
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
            LAYOUT_MARGIN_ZERO,
        )
        main_layout.setSpacing(LAYOUT_SPACING_SMALL)

        # Title
        title = MLabel(UI_TEXT_HAIR_COMPONENT_LIST)
        title.setProperty("h2", True)
        main_layout.addWidget(title)

        # Component list
        self.component_list = MListView()
        self.component_list.setObjectName(
            OBJECT_NAME_COMPONENT_LIST_FORMAT.format(self.hair_type)
        )

        # Set up model for MListView
        self.component_model = QStandardItemModel()
        self.component_list.setModel(self.component_model)

        self.component_list.clicked.connect(self._on_component_clicked)
        main_layout.addWidget(self.component_list)

        # Buttons
        button_layout = MFlowLayout()

        self.add_button = MToolButton()
        set_button_icon_with_fallback(self.add_button, ICON_ADD_LINE)
        self.add_button.clicked.connect(self._on_add_component)
        button_layout.addWidget(self.add_button)

        self.remove_button = MToolButton()
        set_button_icon_with_fallback(self.remove_button, ICON_TRASH_LINE)
        self.remove_button.clicked.connect(self._on_remove_component)
        button_layout.addWidget(self.remove_button)

        main_layout.addLayout(button_layout)

    def _setup_enhanced_interactions(self):
        """Set up enhanced interactions for the component list."""
        # Enable context menu
        self.component_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.component_list.customContextMenuRequested.connect(self._show_context_menu)

        # Enable drag and drop for reordering
        if self._reorder_enabled:
            self.component_list.setDragDropMode(
                QtWidgets.QAbstractItemView.InternalMove
            )
            self.component_list.setDefaultDropAction(Qt.MoveAction)

        # Enable multi-selection if configured
        if self._multi_selection_enabled:
            self.component_list.setSelectionMode(
                QtWidgets.QAbstractItemView.ExtendedSelection
            )
        else:
            self.component_list.setSelectionMode(
                QtWidgets.QAbstractItemView.SingleSelection
            )

        # Install event filter for keyboard shortcuts
        self.component_list.installEventFilter(self)

        # Connect selection changed signal for multi-selection support
        selection_model = self.component_list.selectionModel()
        if selection_model:
            selection_model.selectionChanged.connect(self._on_selection_changed)

    def add_component(self, asset_data):
        """Add a new component to the list.

        Args:
            asset_data (dict): Dictionary containing asset data
        """
        if not asset_data:
            return

        # Create component using manager (only pass asset_id)
        component = self.manager.create_component(asset_data.get("id"))

        if component:
            try:
                # Create custom component item widget
                component_item_widget = ComponentItem(
                    component, parent=self, logger=self._logger
                )

                # Connect signals
                component_item_widget.clicked.connect(self._on_component_item_clicked)
                component_item_widget.visibility_toggled.connect(
                    self._on_component_visibility_toggled
                )

                # Create standard item for the model (correct API for MListView)
                list_item = QStandardItem()
                list_item.setData(component, QtCore.Qt.UserRole)
                list_item.setSizeHint(component_item_widget.sizeHint())

                # Add to model
                self.component_model.appendRow(list_item)

                # Get the index of the new item
                index = self.component_model.indexFromItem(list_item)

                # Set the custom widget for this index
                self.component_list.setIndexWidget(index, component_item_widget)

                # Select the new item
                self.component_list.setCurrentIndex(index)

                # Emit component selected signal
                self.component_selected.emit(component)

                self._logger.debug(
                    "Added component: %s", component.get("name", "Unknown")
                )

            except Exception as e:
                self._logger.warning(
                    "Could not add or select component item: %s", str(e)
                )

    def _create_component_item(self, component):
        """Create a list item for the given component.

        Args:
            component (dict): The component data

        Returns:
            QStandardItem: The created list item for MListView
        """
        # Create QStandardItem for MListView with QStandardItemModel
        item = QStandardItem(component.get("name", "Unnamed Component"))
        item.setData(component, QtCore.Qt.UserRole)
        return item

    def _on_component_clicked(self, index):
        """Handle component item click event.

        Args:
            index (QtCore.QModelIndex): The index of the clicked item
        """
        if not index.isValid():
            return

        # Get component data from model
        model = self.component_list.model()
        component = model.data(index, QtCore.Qt.UserRole)
        if component:
            self.component_selected.emit(component)

    def _on_component_item_clicked(self, component_data):
        """Handle component item widget click event.

        Args:
            component_data (dict): The component data from the clicked item
        """
        # Emit component selected signal with the full component data
        if component_data:
            self.component_selected.emit(component_data)
            self._logger.debug(
                "Component selected: %s", component_data.get("name", "Unknown")
            )

    def _on_component_visibility_toggled(self, component_id, is_visible):
        """Handle component visibility toggle.

        Args:
            component_id (str): The component ID
            is_visible (bool): The new visibility state
        """
        # Update the component data in the manager
        if hasattr(self.manager, "update_component"):
            self.manager.update_component(component_id, is_viewed=is_visible)

        self._logger.debug(
            "Component visibility toggled: %s -> %s",
            component_id,
            "visible" if is_visible else "hidden",
        )

    def _on_add_component(self):
        """Handle add component button click."""
        # This will be implemented to show the asset browser
        pass

    def _on_remove_component(self):
        """Handle remove component button click."""
        # Use model-based approach for MListView
        selection_model = self.component_list.selectionModel()
        if selection_model:
            current_index = selection_model.currentIndex()
            if current_index.isValid():
                self._delete_component_by_index(current_index)

    def clear_components(self):
        """Clear all components from the list."""
        self.component_model.clear()
        self.component_selected.emit(None)

    def get_selected_component(self):
        """Get the currently selected component.

        Returns:
            dict: The selected component data, or None if no selection
        """
        # Use model-based approach for MListView
        selection_model = self.component_list.selectionModel()
        if selection_model:
            current_index = selection_model.currentIndex()
            if current_index.isValid():
                model = self.component_list.model()
                if model:
                    return model.data(current_index, QtCore.Qt.UserRole)
        return None

    def update_components(self, components):
        """Update the component list with the provided components.

        Args:
            components (list): List of component dictionaries to display
        """
        # Clear existing components using model
        self.component_model.clear()

        # Add new components
        for component in components:
            try:
                item = self._create_component_item(component)
                self.component_model.appendRow(item)
            except Exception as e:
                self._logger.warning("Could not add component item: %s", str(e))

        # Force UI update
        self.component_list.update()
        self.component_list.repaint()

    def clear_selection(self):
        """Clear the current selection in the component list."""
        # Clear selection using MListView API
        self.component_list.clearSelection()
        # Emit signal to clear editor
        self.component_selected.emit(None)

    def select_component(self, component_id):
        """Select a component by its ID.

        Args:
            component_id (str): The ID of the component to select
        """
        try:
            # Find the component in the model
            for row in range(self.component_model.rowCount()):
                index = self.component_model.index(row, 0)
                item = self.component_model.itemFromIndex(index)
                if item:
                    component_data = item.data(QtCore.Qt.UserRole)
                    if component_data and component_data.get("id") == component_id:
                        self.component_list.setCurrentIndex(index)
                        self.component_selected.emit(component_data)
                        return
        except Exception as e:
            self._logger.warning(
                "Could not select component %s: %s", component_id, str(e)
            )

    def dragEnterEvent(self, event):
        """Handle drag enter events."""
        if self._can_accept_drop(event):
            event.acceptProposedAction()
            self._set_drop_visual_feedback(True)
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """Handle drag move events."""
        if self._can_accept_drop(event):
            event.acceptProposedAction()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        """Handle drag leave events."""
        self._set_drop_visual_feedback(False)
        event.accept()

    def dropEvent(self, event):
        """Handle drop events."""
        self._set_drop_visual_feedback(False)

        if not self._can_accept_drop(event):
            event.ignore()
            return

        try:
            # Get asset data from the drop
            asset_data = self._extract_asset_data(event)
            if asset_data:
                # Add component from the dropped asset
                self._add_component_from_asset(asset_data)
                event.acceptProposedAction()
            else:
                event.ignore()

        except Exception as e:
            self._logger.error("Error handling drop event: %s", str(e))
            event.ignore()

    def _can_accept_drop(self, event):
        """Check if the drop event can be accepted.

        Args:
            event: The drop event

        Returns:
            bool: True if the drop can be accepted, False otherwise
        """
        # Check if the event has the correct MIME type
        mime_data = event.mimeData()

        # Accept custom hair asset MIME type or plain text
        if mime_data.hasFormat("application/x-hair-asset") or mime_data.hasText():
            return True

        return False

    def _extract_asset_data(self, event):
        """Extract asset data from the drop event.

        Args:
            event: The drop event

        Returns:
            dict or None: Asset data if extraction successful, None otherwise
        """
        mime_data = event.mimeData()

        try:
            # Try custom MIME type first
            if mime_data.hasFormat("application/x-hair-asset"):
                data = mime_data.data("application/x-hair-asset")
                asset_json = data.data().decode("utf-8")
                return json.loads(asset_json)

            # Fallback to plain text
            elif mime_data.hasText():
                asset_json = mime_data.text()
                return json.loads(asset_json)

        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            self._logger.warning("Failed to parse dropped asset data: %s", str(e))

        return None

    def _add_component_from_asset(self, asset_data):
        """Add a component from the dropped asset data.

        Args:
            asset_data (dict): Asset data from the drop operation
        """
        # Check if the asset type matches the current hair type
        asset_type = asset_data.get("asset_type", "")
        if asset_type != self.hair_type:
            self._logger.warning(
                "Asset type '%s' does not match current hair type '%s'",
                asset_type,
                self.hair_type,
            )
            # Still allow the drop but log the mismatch

        # Use the existing add_component method
        self.add_component(asset_data)

        self._logger.info(
            "Added component '%s' from dropped asset", asset_data.get("name", "Unknown")
        )

    def _set_drop_visual_feedback(self, is_active):
        """Set visual feedback for drop operations.

        Args:
            is_active (bool): True to show drop feedback, False to hide
        """
        if is_active:
            # Add visual feedback for drop target
            self.setStyleSheet(
                """
                ComponentList {
                    border: 2px dashed #3E3E3E;
                    background-color: rgba(255, 255, 255, 0.05);
                }
            """
            )
        else:
            # Remove visual feedback
            self.setStyleSheet("")

    def _show_context_menu(self, position):
        """Show context menu for component list.

        Args:
            position (QtCore.QPoint): Position where the context menu was requested
        """
        # Get the index at the position for MListView
        index = self.component_list.indexAt(position)
        component_data = None

        # Get item data if index is valid
        if index.isValid():
            component_data = self.component_model.data(index, QtCore.Qt.UserRole)

        # Create context menu
        context_menu = QtWidgets.QMenu(self)

        if index.isValid() and component_data:
            # Item-specific actions
            component_name = component_data.get("name", "Component")

            # Rename action
            rename_action = context_menu.addAction(f"Rename '{component_name}'")
            rename_action.triggered.connect(
                lambda: self._rename_component_by_index(index)
            )

            # Duplicate action
            duplicate_action = context_menu.addAction(f"Duplicate '{component_name}'")
            duplicate_action.triggered.connect(
                lambda: self._duplicate_component_by_index(index)
            )

            context_menu.addSeparator()

            # Visibility toggle
            is_visible = component_data.get("visible", True)
            visibility_text = "Hide" if is_visible else "Show"
            visibility_action = context_menu.addAction(
                f"{visibility_text} '{component_name}'"
            )
            visibility_action.triggered.connect(
                lambda: self._toggle_component_visibility_by_index(index)
            )

            context_menu.addSeparator()

            # Delete action
            delete_action = context_menu.addAction(f"Delete '{component_name}'")
            delete_action.triggered.connect(
                lambda: self._delete_component_by_index(index)
            )
            delete_action.setIcon(get_icon_with_fallback(ICON_TRASH_LINE))

        # General actions (always available)
        if index.isValid() and component_data:
            context_menu.addSeparator()

        # Add new component action
        add_action = context_menu.addAction("Add New Component")
        add_action.triggered.connect(self._on_add_component)
        add_action.setIcon(get_icon_with_fallback(ICON_ADD_LINE))

        # Selection actions - check count using model
        has_items = self.component_model.rowCount() > 0

        if has_items:
            context_menu.addSeparator()

            select_all_action = context_menu.addAction("Select All")
            select_all_action.triggered.connect(self._select_all_components)

            clear_selection_action = context_menu.addAction("Clear Selection")
            clear_selection_action.triggered.connect(self.clear_selection)

        # Show the menu
        if not context_menu.isEmpty():
            context_menu.exec_(self.component_list.mapToGlobal(position))

    def _rename_component_by_index(self, index):
        """Rename a component by its index.

        Args:
            index (QtCore.QModelIndex): The index of the component to rename
        """
        if not index.isValid():
            return

        model = self.component_list.model()
        if not model:
            return

        component_data = model.data(index, QtCore.Qt.UserRole)
        if not component_data:
            return

        current_name = component_data.get("name", "Unnamed Component")

        # Show input dialog
        new_name, ok = QtWidgets.QInputDialog.getText(
            self,
            "Rename Component",
            "Enter new name:",
            QtWidgets.QLineEdit.Normal,
            current_name,
        )

        if ok and new_name.strip() and new_name.strip() != current_name:
            # Update the component data
            component_data["name"] = new_name.strip()
            model.setData(index, component_data, QtCore.Qt.UserRole)
            model.setData(index, new_name.strip(), QtCore.Qt.DisplayRole)

            # Emit signal
            component_id = component_data.get("id")
            if component_id:
                self.component_renamed.emit(component_id, new_name.strip())

            self._logger.info("Renamed component to '%s'", new_name.strip())

    def _duplicate_component_by_index(self, index):
        """Duplicate a component by its index.

        Args:
            index (QtCore.QModelIndex): The index of the component to duplicate
        """
        if not index.isValid():
            return

        model = self.component_list.model()
        if not model:
            return

        component_data = model.data(index, QtCore.Qt.UserRole)
        if not component_data:
            return

        # Create a copy of the component data
        import copy

        duplicated_data = copy.deepcopy(component_data)

        # Generate new ID and name
        import uuid

        duplicated_data["id"] = str(uuid.uuid4())
        original_name = duplicated_data.get("name", "Component")
        duplicated_data["name"] = f"{original_name} Copy"

        # Add the duplicated component using the existing add_component method
        self.add_component(
            {"id": duplicated_data.get("asset_id", ""), "name": duplicated_data["name"]}
        )

        self._logger.info("Duplicated component '%s'", original_name)

    def _toggle_component_visibility_by_index(self, index):
        """Toggle component visibility by its index.

        Args:
            index (QtCore.QModelIndex): The index of the component to toggle visibility for
        """
        if not index.isValid():
            return

        model = self.component_list.model()
        if not model:
            return

        component_data = model.data(index, QtCore.Qt.UserRole)
        if not component_data:
            return

        # Toggle visibility
        current_visibility = component_data.get("visible", True)
        new_visibility = not current_visibility
        component_data["visible"] = new_visibility

        # Update model data
        model.setData(index, component_data, QtCore.Qt.UserRole)

        # Emit signal
        component_id = component_data.get("id")
        if component_id:
            self.component_visibility_toggled.emit(component_id, new_visibility)

        visibility_text = "shown" if new_visibility else "hidden"
        self._logger.info(
            "Component '%s' %s", component_data.get("name", "Unknown"), visibility_text
        )

    def _delete_component_by_index(self, index):
        """Delete a component by its index with confirmation.

        Args:
            index (QtCore.QModelIndex): The index of the component to delete
        """
        if not index.isValid():
            return

        model = self.component_list.model()
        if not model:
            return

        component_data = model.data(index, QtCore.Qt.UserRole)
        component_name = (
            component_data.get("name", "Component") if component_data else "Component"
        )

        # Show confirmation dialog
        reply = QtWidgets.QMessageBox.question(
            self,
            "Delete Component",
            f"Are you sure you want to delete '{component_name}'?",
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
            QtWidgets.QMessageBox.No,
        )

        if reply == QtWidgets.QMessageBox.Yes:
            # Get component ID before removing
            component_id = component_data.get("id") if component_data else None

            # Remove from model
            model.removeRow(index.row())

            # Emit signals
            if component_id:
                self.component_deleted.emit(component_id)
            self.component_selected.emit(None)

            self._logger.info("Deleted component '%s'", component_name)

    def _select_all_components(self):
        """Select all components in the list."""
        if self._multi_selection_enabled:
            self.component_list.selectAll()
        else:
            self._logger.warning("Multi-selection is not enabled")

    def _on_selection_changed(self, selected, deselected):
        """Handle selection changes for multi-selection support.

        Args:
            selected (QtCore.QItemSelection): Selected items (unused)
            deselected (QtCore.QItemSelection): Deselected items (unused)
        """
        # Note: selected and deselected parameters are required by Qt signal but not used
        # We get the current selection directly from the selection model instead
        # Get currently selected items
        selected_indexes = self.component_list.selectionModel().selectedIndexes()

        if len(selected_indexes) == 1:
            # Single selection - emit the component data
            index = selected_indexes[0]
            model = self.component_list.model()
            component_data = model.data(index, QtCore.Qt.UserRole)
            if component_data:
                self.component_selected.emit(component_data)
        elif len(selected_indexes) > 1:
            # Multi-selection - emit None to clear editor, or implement multi-edit
            self.component_selected.emit(None)
        else:
            # No selection
            self.component_selected.emit(None)

    def eventFilter(self, obj, event):
        """Event filter for keyboard shortcuts and enhanced interactions.

        Args:
            obj: The object that received the event
            event: The event

        Returns:
            bool: True if event was handled, False otherwise
        """
        if obj == self.component_list and event.type() == QtCore.QEvent.KeyPress:
            key = event.key()
            modifiers = event.modifiers()

            # Delete key - delete selected components
            if key == Qt.Key_Delete:
                self._delete_selected_components()
                return True

            # F2 key - rename selected component
            elif key == Qt.Key_F2:
                self._rename_selected_component()
                return True

            # Ctrl+D - duplicate selected component
            elif key == Qt.Key_D and modifiers == Qt.ControlModifier:
                self._duplicate_selected_component()
                return True

            # Ctrl+A - select all
            elif key == Qt.Key_A and modifiers == Qt.ControlModifier:
                self._select_all_components()
                return True

            # Space - toggle visibility
            elif key == Qt.Key_Space:
                self._toggle_selected_component_visibility()
                return True

        return super(ComponentList, self).eventFilter(obj, event)

    def _delete_selected_components(self):
        """Delete all selected components."""
        # Get selected components using model-based approach
        selection_model = self.component_list.selectionModel()
        if not selection_model:
            return

        selected_indexes = selection_model.selectedIndexes()
        if not selected_indexes:
            return

        if len(selected_indexes) == 1:
            self._delete_component_by_index(selected_indexes[0])
        else:
            # Multiple selection - show batch delete confirmation
            reply = QtWidgets.QMessageBox.question(
                self,
                "Delete Components",
                f"Are you sure you want to delete {len(selected_indexes)} components?",
                QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                QtWidgets.QMessageBox.No,
            )

            if reply == QtWidgets.QMessageBox.Yes:
                # Sort indexes in reverse order to delete from bottom to top
                sorted_indexes = sorted(
                    selected_indexes, key=lambda x: x.row(), reverse=True
                )
                for index in sorted_indexes:
                    component_data = self.component_model.data(
                        index, QtCore.Qt.UserRole
                    )
                    component_id = component_data.get("id") if component_data else None

                    # Remove from model
                    self.component_model.removeRow(index.row())

                    # Emit signal
                    if component_id:
                        self.component_deleted.emit(component_id)

                # Clear selection
                self.component_selected.emit(None)
                self._logger.info("Deleted %d components", len(selected_indexes))

    def _rename_selected_component(self):
        """Rename the currently selected component."""
        # Get current selection using model-based approach
        selection_model = self.component_list.selectionModel()
        if selection_model:
            current_index = selection_model.currentIndex()
            if current_index.isValid():
                self._rename_component_by_index(current_index)

    def _duplicate_selected_component(self):
        """Duplicate the currently selected component."""
        # Get current selection using model-based approach
        selection_model = self.component_list.selectionModel()
        if selection_model:
            current_index = selection_model.currentIndex()
            if current_index.isValid():
                self._duplicate_component_by_index(current_index)

    def _toggle_selected_component_visibility(self):
        """Toggle visibility of the currently selected component."""
        # Get current selection using model-based approach
        selection_model = self.component_list.selectionModel()
        if selection_model:
            current_index = selection_model.currentIndex()
            if current_index.isValid():
                self._toggle_component_visibility_by_index(current_index)

    def get_selected_components(self):
        """Get all currently selected components.

        Returns:
            list: List of selected component data dictionaries
        """
        components = []

        # Use model-based approach
        selection_model = self.component_list.selectionModel()
        if selection_model:
            selected_indexes = selection_model.selectedIndexes()
            for index in selected_indexes:
                component_data = self.component_model.data(index, QtCore.Qt.UserRole)
                if component_data:
                    components.append(component_data)

        return components

    def set_multi_selection_enabled(self, enabled):
        """Enable or disable multi-selection.

        Args:
            enabled (bool): True to enable multi-selection, False to disable
        """
        self._multi_selection_enabled = enabled

        if enabled:
            self.component_list.setSelectionMode(
                QtWidgets.QAbstractItemView.ExtendedSelection
            )
        else:
            self.component_list.setSelectionMode(
                QtWidgets.QAbstractItemView.SingleSelection
            )

        self._logger.info("Multi-selection %s", "enabled" if enabled else "disabled")

    def set_reorder_enabled(self, enabled):
        """Enable or disable component reordering.

        Args:
            enabled (bool): True to enable reordering, False to disable
        """
        self._reorder_enabled = enabled

        if enabled:
            self.component_list.setDragDropMode(
                QtWidgets.QAbstractItemView.InternalMove
            )
            self.component_list.setDefaultDropAction(Qt.MoveAction)
        else:
            self.component_list.setDragDropMode(QtWidgets.QAbstractItemView.NoDragDrop)

        self._logger.info(
            "Component reordering %s", "enabled" if enabled else "disabled"
        )

    def get_component_order(self):
        """Get the current order of components.

        Returns:
            list: List of component IDs in current order
        """
        component_ids = []

        # Use model-based approach
        for i in range(self.component_model.rowCount()):
            index = self.component_model.index(i, 0)
            component_data = self.component_model.data(index, QtCore.Qt.UserRole)
            if component_data and "id" in component_data:
                component_ids.append(component_data["id"])

        return component_ids

    def reorder_components(self, component_ids):
        """Reorder components according to the provided ID list.

        Args:
            component_ids (list): List of component IDs in desired order
        """
        # Get all current items by ID
        items_by_id = {}
        for i in range(self.component_model.rowCount()):
            index = self.component_model.index(i, 0)
            component_data = self.component_model.data(index, QtCore.Qt.UserRole)
            if component_data and "id" in component_data:
                items_by_id[component_data["id"]] = component_data

        # Clear the model
        self.component_model.clear()

        # Add items in new order
        for component_id in component_ids:
            if component_id in items_by_id:
                item = self._create_component_item(items_by_id[component_id])
                self.component_model.appendRow(item)

        # Emit reorder signal
        self.components_reordered.emit(component_ids)
        self._logger.info("Reordered %d components", len(component_ids))
