"""Tests for drag and drop functionality."""

import pytest
from qtpy import Qt<PERSON><PERSON>, QtGui, QtWidgets
from unittest.mock import MagicMock, patch

# Import local modules
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_item import AssetItem
from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList


class TestDragAndDrop:
    """Test cases for drag and drop functionality."""

    @pytest.fixture
    def mock_hair_manager(self):
        """Create a mock HairManager."""
        manager = MagicMock()
        manager.get_assets.return_value = [
            {"id": "asset1", "name": "Test Asset 1", "type": "card"},
            {"id": "asset2", "name": "Test Asset 2", "type": "card"},
        ]
        return manager

    @pytest.fixture
    def asset_library(self, mock_hair_manager, qapp_instance, qtbot):
        """Create an AssetLibrary instance for testing."""
        library = AssetLibrary(
            hair_type=HAIR_TYPE_CARD,
            hair_manager=mock_hair_manager,
            parent=None,
        )
        qtbot.addWidget(library)
        library.show()
        return library


    def test_drag_enter_event(self, asset_library, component_list, qtbot):
        """Test that the component list accepts drag enter events with valid data."""
        # Create a mock drag enter event
        mime_data = QtCore.QMimeData()
        mime_data.setText("hair_asset:asset1")
        
        drag = QtGui.QDrag(asset_library)
        drag.setMimeData(mime_data)
        
        # Create a mock event
        event = QtGui.QDragEnterEvent(
            QtCore.QPoint(0, 0),
            QtCore.Qt.CopyAction,
            mime_data,
            QtCore.Qt.LeftButton,
            QtCore.Qt.NoModifier
        )
        
        # Call the event handler directly
        component_list.dragEnterEvent(event)
        
        # Verify the event was accepted
        assert event.isAccepted()
        assert event.dropAction() == QtCore.Qt.CopyAction

    def test_drop_event_adds_component(self, asset_library, component_list, qtbot):
        """Test that dropping an asset adds it to the component list."""
        from unittest.mock import patch
        from qtpy.QtWidgets import QApplication

        # Create a mock drop event
        mime_data = QtCore.QMimeData()
        asset_data = {"id": "asset1", "name": "Test Asset 1", "type": "card"}
        mime_data.setText(f"hair_asset:{asset_data['id']}")

        # Create a mock drop event
        event = QtGui.QDropEvent(
            QtCore.QPoint(0, 0),
            QtCore.Qt.CopyAction,
            mime_data,
            QtCore.Qt.LeftButton,
            QtCore.Qt.NoModifier,
            QtCore.QEvent.Drop
        )

        # Patch the manager's get_assets method and send the event
        with patch.object(component_list.manager, 'get_assets', return_value=[asset_data]):
            with qtbot.waitSignal(component_list.component_added, timeout=1000) as blocker:
                # Send the event via the application to properly simulate a drop
                QApplication.sendEvent(component_list, event)

            # Assert that the component was added and the correct data was emitted
            assert component_list.component_list.model().rowCount() == 1
            assert blocker.args[0] == asset_data
        

        
        # Verify the signal was emitted with the correct data
        assert blocker.args[0] == asset_data

    def test_drag_move_event(self, asset_library, component_list, qtbot):
        """Test that the component list accepts drag move events with valid data."""
        # Create a mock drag move event
        mime_data = QtCore.QMimeData()
        mime_data.setText("hair_asset:asset1")
        
        event = QtGui.QDragMoveEvent(
            QtCore.QPointF(0, 0), 
            QtCore.Qt.CopyAction, 
            mime_data, 
            QtCore.Qt.LeftButton, 
            QtCore.Qt.NoModifier,
            QtCore.QEvent.DragMove
        )
        
        # Call the event handler directly
        component_list.dragMoveEvent(event)
        
        # Verify the event was accepted
        assert event.isAccepted()
        assert event.dropAction() == QtCore.Qt.CopyAction

    def test_drag_leave_event(self, component_list, qtbot):
        """Test that the drag leave event is handled correctly."""
        # Create a mock drag leave event
        event = QtGui.QDragLeaveEvent()
        
        # This should not raise an exception
        component_list.dragLeaveEvent(event)
        
        # No assertions needed, just verifying no exceptions are raised
        assert True
