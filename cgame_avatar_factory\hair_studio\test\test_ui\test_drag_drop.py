"""Tests for drag and drop functionality."""

import pytest
from qtpy import <PERSON>t<PERSON><PERSON>, QtGui, QtWidgets
from unittest.mock import MagicMock, patch

# Import local modules
from cgame_avatar_factory.hair_studio.constants import HAIR_TYPE_CARD
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import Asset<PERSON>ibrary
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_item import AssetItem
from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList


class TestDragAndDrop:
    """Test cases for drag and drop functionality."""

    @pytest.fixture
    def mock_hair_manager(self):
        """Create a mock HairManager."""
        manager = MagicMock()
        manager.get_assets.return_value = [
            {"id": "asset1", "name": "Test Asset 1", "type": "card"},
            {"id": "asset2", "name": "Test Asset 2", "type": "card"},
        ]
        return manager

    @pytest.fixture
    def asset_library(self, mock_hair_manager, qapp_instance, qtbot):
        """Create an AssetLibrary instance for testing."""
        library = AssetLibrary(
            hair_type=HAIR_TYPE_CARD,
            hair_manager=mock_hair_manager,
            parent=None,
        )
        qtbot.addWidget(library)
        library.show()
        return library

    @pytest.fixture
    def component_list(self, qapp_instance, qtbot):
        """Create a ComponentList instance for testing."""
        component_list = ComponentList(parent=None)
        qtbot.addWidget(component_list)
        component_list.show()
        return component_list

    def test_drag_enter_event(self, asset_library, qtbot):
        """Test that the component list accepts drag enter events with valid data."""
        # Create a mock drag enter event
        mime_data = QtCore.QMimeData()
        mime_data.setText("hair_asset:asset1")
        
        drag = QtGui.QDrag(asset_library)
        drag.setMimeData(mime_data)
        
        # Create a mock event
        event = QtGui.QDragEnterEvent(
            QtCore.QPointF(0, 0), 
            QtCore.Qt.CopyAction, 
            mime_data, 
            QtCore.Qt.LeftButton, 
            QtCore.Qt.NoModifier
        )
        
        # Call the event handler directly
        component_list = ComponentList()
        component_list.dragEnterEvent(event)
        
        # Verify the event was accepted
        assert event.isAccepted()
        assert event.dropAction() == QtCore.Qt.CopyAction

    def test_drop_event_adds_component(self, asset_library, component_list, qtbot):
        """Test that dropping an asset adds it to the component list."""
        # Create a mock drop event
        mime_data = QtCore.QMimeData()
        asset_data = {"id": "asset1", "name": "Test Asset 1", "type": "card"}
        mime_data.setText(f"hair_asset:{asset_data['id']}")
        
        # Store the asset in the asset library's mime data
        asset_library.setProperty("asset_data", asset_data)
        
        # Create a mock drop event
        event = QtGui.QDropEvent(
            QtCore.QPointF(0, 0), 
            QtCore.Qt.CopyAction, 
            mime_data, 
            QtCore.Qt.LeftButton, 
            QtCore.Qt.NoModifier,
            QtCore.QEvent.Drop
        )
        
        # Connect to the component_added signal
        with qtbot.waitSignal(component_list.component_added, timeout=1000) as blocker:
            # Call the drop event handler directly
            component_list.dropEvent(event)
        
        # Verify the component was added to the list
        assert component_list.list_widget.count() == 1
        
        # Verify the signal was emitted with the correct data
        assert blocker.args[0] == asset_data

    def test_drag_move_event(self, asset_library, component_list, qtbot):
        """Test that the component list accepts drag move events with valid data."""
        # Create a mock drag move event
        mime_data = QtCore.QMimeData()
        mime_data.setText("hair_asset:asset1")
        
        event = QtGui.QDragMoveEvent(
            QtCore.QPointF(0, 0), 
            QtCore.Qt.CopyAction, 
            mime_data, 
            QtCore.Qt.LeftButton, 
            QtCore.Qt.NoModifier,
            QtCore.QEvent.DragMove
        )
        
        # Call the event handler directly
        component_list.dragMoveEvent(event)
        
        # Verify the event was accepted
        assert event.isAccepted()
        assert event.dropAction() == QtCore.Qt.CopyAction

    def test_drag_leave_event(self, component_list, qtbot):
        """Test that the drag leave event is handled correctly."""
        # Create a mock drag leave event
        event = QtGui.QDragLeaveEvent()
        
        # This should not raise an exception
        component_list.dragLeaveEvent(event)
        
        # No assertions needed, just verifying no exceptions are raised
        assert True
