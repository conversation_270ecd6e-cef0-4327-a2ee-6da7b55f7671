"""Icon utilities for Hair Studio.

This module provides utilities for loading icons with fallback support
to Qt standard icons when custom icons are not available.
"""

# Import standard library
import os
import logging

# Import Qt modules
from qtpy import QtWidgets, QtGui, QtCore

# Import dayu widgets
from dayu_widgets.qt import MIcon

# Import local modules
from cgame_avatar_factory.hair_studio.constants import QT_ICON_FALLBACKS


def get_icon_with_fallback(icon_name, size=None):
    """Get an icon with fallback to Qt standard icons.

    This function first tries to load the icon using MIcon (dayu_widgets).
    If that fails (icon file not found), it falls back to Qt standard icons.

    Args:
        icon_name (str): Name of the icon file or constant
        size (tuple, optional): Icon size as (width, height). Defaults to None.

    Returns:
        QtGui.QIcon: The loaded icon, or a fallback Qt standard icon
    """
    logger = logging.getLogger(__name__)

    try:
        # First try to load with MIcon
        icon = MIcon(icon_name)

        # Test if the icon is valid by trying to get a pixmap
        test_pixmap = icon.pixmap(16, 16)
        if not test_pixmap.isNull():
            logger.debug("Successfully loaded icon: %s", icon_name)
            return icon
        else:
            logger.debug("Icon pixmap is null for: %s, using fallback", icon_name)

    except Exception as e:
        logger.debug(
            "Failed to load icon %s with MIcon: %s, using fallback", icon_name, str(e)
        )

    # Fallback to Qt standard icon
    return get_qt_standard_icon(icon_name, size)


def get_qt_standard_icon(icon_name, size=None):
    """Get a Qt standard icon as fallback.

    Args:
        icon_name (str): Name of the icon (used to look up fallback mapping)
        size (tuple, optional): Icon size as (width, height). Defaults to None.

    Returns:
        QtGui.QIcon: Qt standard icon
    """
    logger = logging.getLogger(__name__)

    # Get the Qt standard icon name from our mapping
    qt_icon_name = QT_ICON_FALLBACKS.get(icon_name, "SP_FileIcon")

    try:
        # Get the application style
        style = QtWidgets.QApplication.style()
        if not style:
            logger.warning("No application style available, creating default icon")
            return create_default_icon(size)

        # Get the standard icon enum value
        icon_enum = getattr(
            QtWidgets.QStyle, qt_icon_name, QtWidgets.QStyle.SP_FileIcon
        )

        # Get the icon from style
        icon = style.standardIcon(icon_enum)

        logger.debug("Using Qt standard icon %s for %s", qt_icon_name, icon_name)
        return icon

    except Exception as e:
        logger.warning("Failed to get Qt standard icon %s: %s", qt_icon_name, str(e))
        return create_default_icon(size)


def create_default_icon(size=None):
    """Create a simple default icon when all else fails.

    Args:
        size (tuple, optional): Icon size as (width, height). Defaults to (16, 16).

    Returns:
        QtGui.QIcon: A simple colored square icon
    """
    if size is None:
        size = (16, 16)

    width, height = size

    # Create a simple colored pixmap
    pixmap = QtGui.QPixmap(width, height)
    pixmap.fill(QtGui.QColor("#4080FF"))  # Blue color

    # Add a simple border
    painter = QtGui.QPainter(pixmap)
    painter.setPen(QtGui.QColor("#FFFFFF"))
    painter.drawRect(0, 0, width - 1, height - 1)
    painter.end()

    return QtGui.QIcon(pixmap)


def get_pixmap_with_fallback(icon_name, width, height):
    """Get a pixmap with fallback support.

    Args:
        icon_name (str): Name of the icon
        width (int): Desired width
        height (int): Desired height

    Returns:
        QtGui.QPixmap: The icon pixmap
    """
    icon = get_icon_with_fallback(icon_name, (width, height))
    return icon.pixmap(width, height)


def set_button_icon_with_fallback(button, icon_name, size=None):
    """Set a button's icon with fallback support.

    Args:
        button (QtWidgets.QAbstractButton): The button to set icon for
        icon_name (str): Name of the icon
        size (tuple, optional): Icon size as (width, height). Defaults to None.
    """
    logger = logging.getLogger(__name__)

    try:
        icon = get_icon_with_fallback(icon_name, size)
        if icon and not icon.isNull():
            button.setIcon(icon)
            logger.debug("Successfully set icon for button: %s", icon_name)
        else:
            logger.warning("Icon is null for button: %s", icon_name)
            # Create a simple default icon
            default_icon = create_default_icon(size)
            button.setIcon(default_icon)
            logger.debug("Used default icon fallback for button: %s", icon_name)
    except Exception as e:
        logger.error("Failed to set button icon for %s: %s", icon_name, str(e))
        # Try to create a simple default icon
        try:
            default_icon = create_default_icon(size)
            button.setIcon(default_icon)
        except Exception as e2:
            logger.error("Even default icon creation failed: %s", str(e2))


def set_label_pixmap_with_fallback(label, icon_name, width, height):
    """Set a label's pixmap with fallback support.

    Args:
        label (QtWidgets.QLabel): The label to set pixmap for
        icon_name (str): Name of the icon
        width (int): Desired width
        height (int): Desired height
    """
    logger = logging.getLogger(__name__)

    try:
        pixmap = get_pixmap_with_fallback(icon_name, width, height)
        if pixmap and not pixmap.isNull():
            label.setPixmap(pixmap)
            logger.debug(
                "Successfully set pixmap for label: %s (%dx%d)",
                icon_name,
                width,
                height,
            )
        else:
            logger.warning("Pixmap is null for icon: %s", icon_name)
            # Create a simple colored rectangle as ultimate fallback
            fallback_pixmap = QtGui.QPixmap(width, height)
            fallback_pixmap.fill(QtGui.QColor("#4080FF"))
            label.setPixmap(fallback_pixmap)
            logger.debug("Used colored rectangle fallback for: %s", icon_name)
    except Exception as e:
        logger.error("Failed to set label pixmap for %s: %s", icon_name, str(e))
        # Create a simple colored rectangle as ultimate fallback
        try:
            fallback_pixmap = QtGui.QPixmap(width, height)
            fallback_pixmap.fill(
                QtGui.QColor("#FF4080")
            )  # Different color to indicate error
            label.setPixmap(fallback_pixmap)
        except Exception as e2:
            logger.error("Even fallback pixmap creation failed: %s", str(e2))
