"""Test Drag and Drop Logic (No Qt Dependencies).

This test script verifies the core logic of drag and drop functionality
without requiring Qt libraries.
"""

import sys
import os
import json

# Add the project root to the Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
sys.path.insert(0, project_root)

def test_asset_data_serialization():
    """Test asset data serialization and deserialization."""
    print("=" * 60)
    print("资产数据序列化测试")
    print("=" * 60)
    
    try:
        # Sample asset data
        sample_assets = [
            {
                "id": "card_1",
                "name": "Basic Hair Card",
                "asset_type": "card",
                "thumbnail": "card_1_thumb.jpg",
                "file_path": "assets/cards/basic_hair_card.ma",
            },
            {
                "id": "xgen_1",
                "name": "Fine Hair XGen",
                "asset_type": "xgen",
                "thumbnail": "xgen_1_thumb.jpg",
                "file_path": "assets/xgen/fine_hair.xgen",
            },
            {
                "id": "curve_1",
                "name": "Guide Curves",
                "asset_type": "curve",
                "thumbnail": "curve_1_thumb.jpg",
                "file_path": "assets/curves/guide_curves.ma",
            }
        ]
        
        print("✓ 测试资产数据序列化...")
        for asset in sample_assets:
            # Serialize to JSON
            asset_json = json.dumps(asset)
            
            # Deserialize from JSON
            parsed_asset = json.loads(asset_json)
            
            # Verify data integrity
            assert parsed_asset == asset, f"Data mismatch for asset {asset['id']}"
            print(f"  ✓ {asset['name']} ({asset['asset_type']})")
        
        print("✓ 所有资产数据序列化测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 序列化测试失败: {str(e)}")
        return False

def test_mock_data_manager():
    """Test mock data manager functionality."""
    print("\n" + "=" * 60)
    print("模拟数据管理器测试")
    print("=" * 60)
    
    try:
        # Import mock data manager
        from cgame_avatar_factory.hair_studio.data.mock_data_manager import MockDataManager
        
        # Create manager
        manager = MockDataManager()
        print("✓ MockDataManager创建成功")
        
        # Test getting assets
        all_assets = manager.get_assets()
        print(f"✓ 获取所有资产: {len(all_assets)} 个")
        
        # Test filtering by type
        card_assets = manager.get_assets("card")
        xgen_assets = manager.get_assets("xgen")
        curve_assets = manager.get_assets("curve")
        
        print(f"  - Card资产: {len(card_assets)} 个")
        print(f"  - XGen资产: {len(xgen_assets)} 个")
        print(f"  - Curve资产: {len(curve_assets)} 个")
        
        # Test component creation
        if card_assets:
            asset_id = card_assets[0]["id"]
            component = manager.create_component(asset_id)
            if component:
                print(f"✓ 组件创建成功: {component['name']}")
            else:
                print("✗ 组件创建失败")
                return False
        
        # Test getting components
        components = manager.get_components()
        print(f"✓ 获取所有组件: {len(components)} 个")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_hair_manager():
    """Test hair manager functionality."""
    print("\n" + "=" * 60)
    print("毛发管理器测试")
    print("=" * 60)
    
    try:
        # Import hair manager
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # Create manager
        manager = HairManager()
        print("✓ HairManager创建成功")
        
        # Test getting assets
        card_assets = manager.get_assets("card")
        print(f"✓ 获取Card资产: {len(card_assets)} 个")
        
        # Test component creation
        if card_assets:
            asset_id = card_assets[0]["id"]
            component = manager.create_component(asset_id)
            if component:
                print(f"✓ 组件创建成功: {component['name']}")
                
                # Test component retrieval
                retrieved = manager.get_component(component["id"])
                if retrieved:
                    print(f"✓ 组件检索成功: {retrieved['name']}")
                else:
                    print("✗ 组件检索失败")
                    return False
            else:
                print("✗ 组件创建失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 毛发管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_drag_drop_data_flow():
    """Test the complete drag and drop data flow."""
    print("\n" + "=" * 60)
    print("拖拽数据流测试")
    print("=" * 60)
    
    try:
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # Simulate drag and drop workflow
        manager = HairManager()
        
        # 1. Get asset from library (drag source)
        assets = manager.get_assets("card")
        if not assets:
            print("✗ 没有可用的资产")
            return False
        
        source_asset = assets[0]
        print(f"✓ 源资产: {source_asset['name']}")
        
        # 2. Serialize asset data (drag operation)
        asset_json = json.dumps(source_asset)
        print("✓ 资产数据序列化完成")
        
        # 3. Deserialize asset data (drop operation)
        dropped_asset = json.loads(asset_json)
        print("✓ 资产数据反序列化完成")
        
        # 4. Verify data integrity
        assert dropped_asset == source_asset, "数据完整性检查失败"
        print("✓ 数据完整性验证通过")
        
        # 5. Create component from dropped asset
        component = manager.create_component(dropped_asset["id"])
        if component:
            print(f"✓ 从拖拽资产创建组件: {component['name']}")
        else:
            print("✗ 组件创建失败")
            return False
        
        # 6. Verify component in list
        components = manager.get_components("card")
        component_names = [c["name"] for c in components]
        if any("New " + source_asset["name"] in name for name in component_names):
            print("✓ 组件已添加到列表")
        else:
            print("✗ 组件未在列表中找到")
            return False
        
        print("✓ 完整拖拽数据流测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 拖拽数据流测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始毛发工作室拖拽逻辑测试...")
    
    # Run all tests
    tests = [
        test_asset_data_serialization,
        test_mock_data_manager,
        test_hair_manager,
        test_drag_drop_data_flow
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
        print("✓ 资产数据序列化正常")
        print("✓ 数据管理器功能正常")
        print("✓ 毛发管理器功能正常")
        print("✓ 拖拽数据流完整")
        print("\n✅ 拖拽功能核心逻辑实现完成!")
    else:
        print(f"❌ 部分测试失败 ({passed}/{total})")
        print("请检查上述错误信息")
    
    print("\n测试完成。")
