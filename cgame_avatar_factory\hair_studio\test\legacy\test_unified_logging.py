#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试毛发工作室统一日志管理
验证logger传递机制是否正常工作
"""

import sys
import os
import logging
import io

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_mock_modules():
    """设置必要的Mock模块"""
    import types
    
    # Mock maya.app.general.mayaMixin
    maya_app = types.ModuleType('maya.app')
    maya_app.general = types.ModuleType('maya.app.general')
    maya_app.general.mayaMixin = types.ModuleType('maya.app.general.mayaMixin')
    
    class MayaQWidgetDockableMixin:
        def setDockableParameters(self, dockable):
            pass
    
    maya_app.general.mayaMixin.MayaQWidgetDockableMixin = MayaQWidgetDockableMixin
    
    sys.modules['maya.app'] = maya_app
    sys.modules['maya.app.general'] = maya_app.general
    sys.modules['maya.app.general.mayaMixin'] = maya_app.general.mayaMixin
    
    # Mock lightbox_ui.banner
    lightbox_ui = sys.modules.get('lightbox_ui')
    if lightbox_ui is None:
        lightbox_ui = types.ModuleType('lightbox_ui')
        sys.modules['lightbox_ui'] = lightbox_ui
    
    if not hasattr(lightbox_ui, 'banner'):
        lightbox_ui.banner = types.ModuleType('lightbox_ui.banner')
        lightbox_ui.banner.setup_banner = lambda *args, **kwargs: None
        sys.modules['lightbox_ui.banner'] = lightbox_ui.banner

def test_unified_logging():
    """测试统一日志管理"""
    print("=" * 60)
    print("毛发工作室统一日志管理测试")
    print("=" * 60)
    
    try:
        # 设置Mock模块
        setup_mock_modules()
        
        # 创建一个自定义logger，模拟主窗口的logger
        main_logger = logging.getLogger("test_main_window")
        main_logger.setLevel(logging.DEBUG)
        
        # 创建一个StringIO来捕获日志输出
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        main_logger.addHandler(handler)
        
        print("✓ 主窗口logger设置完成")
        
        # 测试1: 直接导入模拟数据管理器
        print("\n1. 测试模拟数据管理器...")
        from cgame_avatar_factory.hair_studio.data.mock_data_manager import MockDataManager
        
        mock_manager = MockDataManager()
        components = mock_manager.get_components()
        print(f"   ✓ 模拟数据管理器工作正常，获取到 {len(components)} 个组件")
        
        # 测试2: 测试HairManager的logger传递
        print("\n2. 测试HairManager的logger传递...")
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # 使用主logger创建HairManager
        hair_manager = HairManager(logger=main_logger)
        
        # 触发一些日志记录
        hair_manager._logger.info("HairManager初始化完成")
        hair_manager._logger.debug("测试debug日志")
        
        # 测试组件操作
        assets = hair_manager.get_assets()
        if assets:
            component = hair_manager.create_component(assets[0]["id"])
            if component:
                hair_manager._logger.info("成功创建组件: %s", component["name"])
        
        print("   ✓ HairManager logger传递正常")
        
        # 测试3: 测试UI组件的logger传递（不启动UI）
        print("\n3. 测试UI组件的logger传递...")
        
        # 测试ComponentList
        from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
        component_list = ComponentList("card", hair_manager, logger=main_logger)
        component_list._logger.info("ComponentList初始化完成")
        print("   ✓ ComponentList logger传递正常")
        
        # 测试EditorArea
        from cgame_avatar_factory.hair_studio.ui.editor_area import EditorArea
        editor_area = EditorArea("card", hair_manager, logger=main_logger)
        editor_area._logger.info("EditorArea初始化完成")
        print("   ✓ EditorArea logger传递正常")
        
        # 测试AssetLibrary
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
        asset_library = AssetLibrary("card", hair_manager, logger=main_logger)
        asset_library._logger.info("AssetLibrary初始化完成")
        print("   ✓ AssetLibrary logger传递正常")
        
        # 测试4: 测试BaseHairTab的logger传递
        print("\n4. 测试BaseHairTab的logger传递...")
        from cgame_avatar_factory.hair_studio.ui.base_hair_tab import BaseHairTab
        
        base_tab = BaseHairTab("card", hair_manager, logger=main_logger)
        base_tab._logger.info("BaseHairTab初始化完成")
        print("   ✓ BaseHairTab logger传递正常")
        
        # 测试5: 测试HairStudioTab的logger传递
        print("\n5. 测试HairStudioTab的logger传递...")
        from cgame_avatar_factory.hair_studio.ui.hair_studio_tab import HairStudioTab
        
        hair_studio_tab = HairStudioTab(logger=main_logger)
        hair_studio_tab._logger.info("HairStudioTab初始化完成")
        print("   ✓ HairStudioTab logger传递正常")
        
        # 检查日志输出
        print("\n6. 检查日志输出...")
        log_output = log_stream.getvalue()
        log_lines = log_output.strip().split('\n')
        
        print(f"   ✓ 捕获到 {len(log_lines)} 条日志记录")
        
        # 验证所有组件都使用了同一个logger名称
        expected_logger_name = "test_main_window"
        for line in log_lines:
            if line.strip():  # 跳过空行
                logger_name = line.split(' - ')[0]
                if logger_name != expected_logger_name:
                    print(f"   ✗ 发现不一致的logger名称: {logger_name}")
                    return False
        
        print(f"   ✓ 所有组件都使用了统一的logger: {expected_logger_name}")
        
        # 显示部分日志内容
        print("\n7. 日志输出示例:")
        for i, line in enumerate(log_lines[:5]):  # 显示前5条日志
            if line.strip():
                print(f"   {i+1}. {line}")
        if len(log_lines) > 5:
            print(f"   ... 还有 {len(log_lines) - 5} 条日志")
        
        print("\n" + "=" * 60)
        print("统一日志管理测试完成！")
        print("✓ 所有测试通过")
        print("✓ Logger传递机制正常工作")
        print("✓ 所有组件使用统一的logger实例")
        print("✓ 日志输出格式正确")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_unified_logging()
    if success:
        print("\n✓ 统一日志管理测试成功")
    else:
        print("\n✗ 统一日志管理测试失败")
        sys.exit(1)
