# ComponentItem Layout Fix - 组件列表显示问题解决方案

## 问题描述

用户报告组件列表中的前两个元素（眼睛图标和资产图标）没有显示，只能看到组件名称。

### 预期布局
组件列表中每个项目应该按以下顺序显示：
1. **眼睛图标** - 控制组件可见性（第一个，最左侧）
2. **资产图标** - 从毛发素材库继承的小图标（第二个，中间）
3. **组件名称** - 显示组件名字（第三个，最右侧，可拉伸）

### 实际问题
- 前两个图标不显示
- 只能看到组件名称
- 布局顺序可能不正确

## 问题分析

通过代码分析发现了以下问题：

### 1. 布局顺序错误
**原始代码顺序**：
```python
layout.addWidget(self.asset_icon)        # 资产图标 - 第一个
layout.addWidget(self.name_label, 1)     # 名称 - 第二个  
layout.addWidget(self.visibility_button) # 眼睛图标 - 第三个
```

**正确顺序应该是**：
```python
layout.addWidget(self.visibility_button) # 眼睛图标 - 第一个
layout.addWidget(self.asset_icon)        # 资产图标 - 第二个
layout.addWidget(self.name_label, 1)     # 名称 - 第三个
```

### 2. 图标文件缺失
- 眼睛图标：`eye_line.svg`、`eye_off_line.svg` - 文件不存在
- 资产图标：`card_line.svg`、`xgen_line.svg`、`curve_line.svg` - 文件不存在
- 这些图标需要依赖Qt标准图标回退系统

### 3. 错误处理不足
- 图标加载失败时没有适当的错误处理
- 缺少调试信息来诊断图标加载问题

## 解决方案

### 1. 修复布局顺序

**文件**: `cgame_avatar_factory/hair_studio/ui/component_item.py`

```python
def setup_ui(self):
    """Set up the user interface."""
    # Main horizontal layout
    layout = QtWidgets.QHBoxLayout(self)
    layout.setContentsMargins(5, 2, 5, 2)
    layout.setSpacing(5)

    # 1. Visibility toggle button (eye icon) - FIRST
    self.visibility_button = MPushButton()
    self.visibility_button.setFixedSize(20, 20)
    self.visibility_button.clicked.connect(self._on_visibility_clicked)
    self._update_visibility_button()

    # 2. Asset type icon - SECOND  
    self.asset_icon = MLabel()
    self.asset_icon.setFixedSize(16, 16)
    self._set_asset_icon()

    # 3. Component name label - THIRD
    self.name_label = MLabel(self.component_data.get("name", "Unnamed Component"))
    self.name_label.set_elide_mode(Qt.ElideRight)
    self.name_label.setToolTip(self.name_label.text())

    # Add widgets to layout in correct order: eye -> asset icon -> name
    layout.addWidget(self.visibility_button)
    layout.addWidget(self.asset_icon)
    layout.addWidget(self.name_label, 1)  # Stretch to fill available space
```

### 2. 增强图标设置函数

添加了更好的错误处理和调试信息：

```python
def _set_asset_icon(self):
    """Set the appropriate asset type icon."""
    asset_type = self.component_data.get("type", "card")
    icon_map = {
        "card": ICON_HAIR_CARD,
        "xgen": ICON_HAIR_XGEN,
        "curve": ICON_HAIR_CURVE,
    }
    icon_name = icon_map.get(asset_type, ICON_HAIR_CARD)
    
    try:
        set_label_pixmap_with_fallback(self.asset_icon, icon_name, 16, 16)
        self._logger.debug("Asset icon set for type %s: %s", asset_type, icon_name)
    except Exception as e:
        self._logger.warning("Failed to set asset icon for %s: %s", asset_type, str(e))
        # Set a simple background color as fallback
        self.asset_icon.setStyleSheet(
            "QLabel { background-color: #4080FF; border: 1px solid #FFFFFF; }"
        )

def _update_visibility_button(self):
    """Update the visibility button icon based on component state."""
    is_visible = self.component_data.get("is_viewed", True)

    try:
        if is_visible:
            set_button_icon_with_fallback(self.visibility_button, ICON_EYE_LINE)
            self.visibility_button.setToolTip("Hide component")
            self.visibility_button.setText("")  # Clear any text
        else:
            set_button_icon_with_fallback(self.visibility_button, ICON_EYE_OFF_LINE)
            self.visibility_button.setToolTip("Show component")
            self.visibility_button.setText("")  # Clear any text
            
        self._logger.debug("Visibility button updated: %s", "visible" if is_visible else "hidden")
        
    except Exception as e:
        self._logger.warning("Failed to set visibility icon: %s", str(e))
        # Fallback to text-based button
        if is_visible:
            self.visibility_button.setText("👁")  # Eye emoji as fallback
            self.visibility_button.setToolTip("Hide component")
        else:
            self.visibility_button.setText("🚫")  # No entry emoji as fallback
            self.visibility_button.setToolTip("Show component")
```

### 3. 改进图标工具函数

**文件**: `cgame_avatar_factory/hair_studio/utils/icon_utils.py`

增强了错误处理和回退机制：

```python
def set_label_pixmap_with_fallback(label, icon_name, width, height):
    """Set a label's pixmap with fallback support."""
    logger = logging.getLogger(__name__)
    
    try:
        pixmap = get_pixmap_with_fallback(icon_name, width, height)
        if pixmap and not pixmap.isNull():
            label.setPixmap(pixmap)
            logger.debug("Successfully set pixmap for label: %s (%dx%d)", icon_name, width, height)
        else:
            logger.warning("Pixmap is null for icon: %s", icon_name)
            # Create a simple colored rectangle as ultimate fallback
            fallback_pixmap = QtGui.QPixmap(width, height)
            fallback_pixmap.fill(QtGui.QColor("#4080FF"))
            label.setPixmap(fallback_pixmap)
            logger.debug("Used colored rectangle fallback for: %s", icon_name)
    except Exception as e:
        logger.error("Failed to set label pixmap for %s: %s", icon_name, str(e))
        # Create a simple colored rectangle as ultimate fallback
        try:
            fallback_pixmap = QtGui.QPixmap(width, height)
            fallback_pixmap.fill(QtGui.QColor("#FF4080"))  # Different color to indicate error
            label.setPixmap(fallback_pixmap)
        except Exception as e2:
            logger.error("Even fallback pixmap creation failed: %s", str(e2))
```

## 图标回退策略

### 回退链
1. **自定义图标文件** (如果存在)
2. **Qt标准图标** (根据映射)
3. **彩色方块** (最终回退)
4. **文本/表情符号** (按钮的额外回退)

### 图标映射
```python
# 眼睛图标
ICON_EYE_LINE -> SP_DialogApplyButton -> 蓝色方块 -> "👁"
ICON_EYE_OFF_LINE -> SP_DialogCancelButton -> 蓝色方块 -> "🚫"

# 资产图标  
ICON_HAIR_CARD -> SP_FileIcon -> 蓝色方块
ICON_HAIR_XGEN -> SP_DirIcon -> 蓝色方块
ICON_HAIR_CURVE -> SP_FileDialogListView -> 蓝色方块
```

## 测试验证

创建了多个测试脚本验证修复：

1. **`test_component_item_display.py`** - 布局和图标映射测试
2. **`visual_component_test.py`** - 可视化测试（需要Qt环境）

### 测试结果
- ✅ 布局顺序已修正
- ✅ 图标回退系统正常工作
- ✅ 错误处理和调试信息完善
- ✅ 所有测试通过

## 预期效果

修复后，组件列表中每个项目应该显示：

```
[👁] [📄] Hair Card Component
[🚫] [📁] XGen Hair Component  
[👁] [📋] Hair Curve Component
```

其中：
- 第一个图标：眼睛图标（可见性控制）
- 第二个图标：资产类型图标（Qt标准图标或彩色方块）
- 第三个元素：组件名称

## 调试建议

如果图标仍然不显示：

1. **启用调试日志**：
   ```python
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **检查控制台输出**：
   - 查找图标加载的调试信息
   - 注意警告和错误消息

3. **验证Qt环境**：
   - 确保Qt应用程序样式可用
   - 检查dayu_widgets是否正常工作

4. **手动测试**：
   - 运行 `visual_component_test.py` 进行可视化验证
   - 使用 `debug_start_hair_dev_direct.cmd` 启动完整环境

## 总结

通过修复布局顺序、增强错误处理和改进图标回退系统，解决了组件列表中图标不显示的问题。现在组件项应该能够正确显示所有三个元素，即使在图标文件缺失的情况下也能提供适当的视觉反馈。
